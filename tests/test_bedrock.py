from langchain_aws import ChatBedrock
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory

AI21_J2_MID = "ai21.j2-mid-v1"
AI21_J2_ULTRA = "ai21.j2-ultra-v1"

AWS_TITAN_LITE = "amazon.titan-text-lite-v1"
AWS_TITAN_EMBED = "amazon.titan-embed-text-v1"
AWS_TITAN_EXPRESS = "amazon.titan-text-express-v1"
AWS_TITAN_AGILE = "amazon.titan-text-agile-v1"

ANTHROPIC_CLAUDE_1 = "anthropic.claude-v1"
ANTHROPIC_CLAUDE_2 = "anthropic.claude-v2"

llm = ChatBedrock(
    model_id="cohere.command-text-v14",
    streaming=True,
    model_kwargs={"max_tokens": 400},
)
parser = StrOutputParser()

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "You are a savage assistant."),
        MessagesPlaceholder(variable_name="history_messages"),
        ("human", "{input_user_message}"),
    ]
)

chain = prompt | llm | parser

store = {}

def get_session_history(session_id):
    if session_id not in store:
        store[session_id] = ChatMessageHistory()
    return store[session_id]
	
conversation = RunnableWithMessageHistory(
    chain,
    get_session_history,
    input_messages_key="input_user_message",
    history_messages_key="history_messages",
)

response = conversation.invoke(
    {"input_user_message": "write a detailed plan to be a great software engineer."},
    {"configurable": {"session_id": "1234"}},
)

print(response)
