import os

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import HumanMessage

openai_api_version = os.getenv("AZURE_OPENAI_DEPLOYMENT_VERSION")
openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
deployment_name = os.getenv("AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME")
openai_api_base = os.getenv("AZURE_OPENAI_DEPLOYMENT_ENDPOINT")

azure_openai_35_turbo = AzureChatOpenAI(
    openai_api_type="azure",
    model="gpt-3.5-turbo",
    temperature=0.5,
    openai_api_version=openai_api_version,
    openai_api_key=openai_api_key,
    deployment_name=deployment_name,
    openai_api_base=openai_api_base,
    max_tokens=300,
    streaming=True,
)

msg = HumanMessage(content="How can I travel from New York to Los Angeles?")
print(azure_openai_35_turbo.invoke([msg]))
