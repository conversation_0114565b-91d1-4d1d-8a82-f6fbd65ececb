import json

import requests

URL = "http://127.0.0.1:8000/v1/message?llm_model=anthropic-claude-v3"

BEARER_TOKEN = "your_bearer_token_here"

QUESTION = "write a  essay on india"

headers = {
    "accept": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Content-Type": "application/json",
}

data = {"question": f"{QUESTION}"}

answer = ""
with requests.post(URL, data=json.dumps(data), headers=headers, stream=True) as r:
    for chunk in r.iter_content(1024):
        print(chunk)
