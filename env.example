# Project Configuration
PROJECT_NAME=document-data-extraction
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=local
BACKEND_CORS_ORIGINS=["*"]
SHOW_SWAGGER_DOC=true

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_SESSION_TOKEN=your_aws_session_token  # Optional, for temporary credentials
AWS_REGION=us-east-1
AWS_DEFAULT_REGION=us-east-1

# S3 Configuration
S3_BUCKET_NAME=your-s3-bucket-name
FOLDER_PREFIX=documents

# SQS Configuration (for queue processing)
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/your-account-id/your-queue-name

# OpenSearch Configuration
OPENSEARCH_URL=http://localhost:9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_VECTOR_INDEX=documents

# LangChain Configuration (Optional)
LANGCHAIN_TRACING_V2=false
LANGCHAIN_ENDPOINT=
LANGCHAIN_API_KEY=
LANGCHAIN_PROJECT=

# OpenAI Configuration (Optional)
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration (Optional)
AZURE_OPENAI_DEPLOYMENT_ENDPOINT=
AZURE_OPENAI_DEPLOYMENT_VERSION=
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME=
AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME=
AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379
REDIS_PORT=6379
REDIS_TTL=604800

# CloudWatch Configuration (Optional)
AWS_CLOUDWATCH_LOG_GROUP=document-data-extraction-api
AWS_CLOUDWATCH_LOG_STREAM=document-data-extraction-api-ls 