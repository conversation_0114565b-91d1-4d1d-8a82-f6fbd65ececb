"""
A simple script to test the S3 connection using the S3Service.
"""
import sys
from pathlib import Path

# Add the project root to the Python path to allow for absolute imports
# This is necessary because we are running a script from the root, not as a module
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from loguru import logger
from botocore.exceptions import NoCredentialsError, ClientError

# These imports will work because of the sys.path.append above
from app.core.configuration import settings
from app.services.s3_service import s3_service


def test_s3_list_files():
    """
    Tests the S3 connection by attempting to list files in the configured bucket.
    """
    logger.info("--- Starting S3 Connection Test ---")

    # 1. Check if the bucket name is configured
    bucket_name = settings.S3_BUCKET_NAME
    if not bucket_name:
        logger.error(
            "S3_BUCKET_NAME is not set in your .env file. Please configure it before running the test."
        )
        logger.info("--- S3 Connection Test Failed ---")
        return

    logger.info(f"Attempting to list files in bucket: '{bucket_name}'")

    try:
        # 2. Call the list_files method from the s3_service
        files = s3_service.list_files(bucket_name=bucket_name, max_keys=10)

        # 3. Report the results
        logger.success("Successfully connected to S3 and listed files.")
        logger.info(f"Found {len(files)} file(s) in the bucket (showing up to 10):")

        if not files:
            logger.info("The bucket is empty or the specified prefix contains no files.")
        else:
            for file_info in files:
                logger.info(f" - Key: {file_info['key']}, Size: {file_info['size']} bytes")

        logger.info("--- S3 Connection Test Passed ---")

    except (NoCredentialsError, ClientError) as e:
        logger.error(f"Could not connect to S3. Error: {e}")
        logger.error("Please check your AWS credentials and bucket name in the .env file.")
        logger.info("--- S3 Connection Test Failed ---")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        logger.info("--- S3 Connection Test Failed ---")


if __name__ == "__main__":
    test_s3_list_files()