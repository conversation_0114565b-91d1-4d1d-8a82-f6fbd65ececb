# Document Data Extraction API

A FastAPI-based service for document processing, S3 file management, and vector storage with OpenSearch.

## Features

- **S3 File Management**: Upload, download, list, and delete files from AWS S3
- **Document Processing**: Extract and process documents for vector storage
- **Vector Storage**: Store and search document embeddings using OpenSearch
- **RESTful API**: Clean, well-documented API endpoints
- **Health Monitoring**: Service health checks and monitoring

## Quick Start

### Prerequisites

- Python 3.8+
- AWS Account with S3 access
- OpenSearch instance (optional, for vector storage)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd document-data-extraction
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp env.example .env
   # Edit .env with your AWS credentials and configuration
   ```

5. **Run the application**
   ```bash
   python -m app.main
   ```

The API will be available at `http://localhost:8000`

## Environment Configuration

Copy `env.example` to `.env` and configure the following variables:

### Required AWS Configuration
```bash
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket-name
S3_BUCKET_NAME=your-s3-bucket-name
```

### Optional Configuration
```bash
# OpenSearch (for vector storage)
OPENSEARCH_URL=http://localhost:9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin

# SQS (for queue processing)
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/your-account-id/your-queue-name
```

## API Endpoints

### S3 Operations

#### Upload File
```http
POST /v1/s3/upload
Content-Type: multipart/form-data

file: <file>
bucket_name: <bucket_name>
folder_prefix: <optional_prefix>
custom_filename: <optional_filename>
```

#### Upload Multiple Files
```http
POST /v1/s3/upload-multiple
Content-Type: multipart/form-data

files: <file1>, <file2>, ...
bucket_name: <bucket_name>
folder_prefix: <optional_prefix>
```

#### List Files
```http
GET /v1/s3/list/{bucket_name}?folder_prefix=<prefix>&max_keys=<max>
```

#### Download File
```http
GET /v1/s3/download/{bucket_name}/{object_key}
```

#### Delete File
```http
DELETE /v1/s3/delete
Content-Type: application/x-www-form-urlencoded

bucket_name: <bucket_name>
object_key: <object_key>
```

#### Generate Presigned URL
```http
POST /v1/s3/presigned-url
Content-Type: application/json

{
  "bucket_name": "my-bucket",
  "object_key": "path/to/file.pdf",
  "expires_in": 3600
}
```

#### Get Bucket Info
```http
GET /v1/s3/bucket/{bucket_name}
```

#### Health Check
```http
GET /v1/s3/health
```

### Document Processing

#### Ingest File
```http
POST /v1/files/ingest
Content-Type: multipart/form-data

file: <file>
chunk_size: 500
chunk_overlap: 50
```

#### Upload and Ingest
```http
POST /v1/files/upload-and-ingest
Content-Type: multipart/form-data

file: <file>
bucket_name: <bucket_name>
folder_prefix: <optional_prefix>
chunk_size: 500
chunk_overlap: 50
```

### Health Check
```http
GET /v1/health
```

## API Documentation

Once the application is running, you can access:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

## Project Structure

```
document-data-extraction/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   ├── s3.py          # S3 operations
│   │   │   ├── ingestion.py   # Document processing
│   │   │   └── health.py      # Health checks
│   │   ├── dependencies.py    # API dependencies
│   │   └── routers.py         # Route configuration
│   ├── core/
│   │   └── configuration.py   # App configuration
│   ├── services/
│   │   └── s3_service.py      # S3 business logic
│   ├── schemas/
│   │   ├── s3.py             # S3 data models
│   │   └── base.py           # Base schemas
│   └── main.py               # Application entry point
├── requirements.txt
├── env.example
└── README.md
```

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black app/
isort app/
```

### Linting
```bash
flake8 app/
mypy app/
```

## Docker

### Build Image
```bash
docker build -t document-data-extraction .
```

### Run Container
```bash
docker run -p 8000:8000 --env-file .env document-data-extraction
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.