# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar


# Maven
target/

#IDE
.idea
.vscode

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Python
.idea
.ipynb_checkpoints
.mypy_cache
__pycache__
.pytest_cache
htmlcov
dist
site
.coverage
coverage.xml
.netlify
test.db
log.txt
Pipfile.lock
env3.*
docs_build
venv
docs.zip
archive.zip
*.env
!sample.env

backend.env
env/backend.env
**/backend.env

alembic.ini

celerybeat-schedule

# vim temporary files
*~
.*.sw?
/myenv/
codebuild_build.sh
taskdefsvc.json
taskdefconsmr.json