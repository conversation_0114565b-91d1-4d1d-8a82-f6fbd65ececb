version: '3.9'

services:
  # OpenSearch
  opensearch:
    image: opensearchproject/opensearch:latest
    environment:
      discovery.type: single-node
      OPENSEARCH_INITIAL_ADMIN_PASSWORD: uqCS6&@v%W]iwh}9$Dgk
    volumes:
      - opensearch_data:/usr/share/opensearch/data
    ports:
      - '9200:9200'
      - '9300:9300'

  # Redis
  redis:
    image: redis:6.2-alpine
    ports:
      - '6380:6379'
    volumes:
      - redis_data:/data

  # Postgres
  postgres:
    image: postgres:14.1-alpine
    # Use environment variables from .env file
    env_file:
      - backend.env
    ports:
      - '5434:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # FastAPI backend
  backend:
    container_name: backend
    build:
      context: .  # Root directory
      dockerfile: Dockerfile  # Explicitly specify backend Dockerfile
    env_file:
      - .env
    ports:
      - '8000:8000' # Replace with desired port for your FastAPI app
    depends_on:
      - opensearch # redis and postgres are optional here
    networks:
      - application

  # Streamlit frontend
  streamlit_app:
    container_name: frontend
    build:
      context: .  # Root directory
      dockerfile: streamlit.Dockerfile
    ports:
      - '8501:8501'
    env_file:
      - .env
    depends_on:
      - backend  # Ensure backend starts first
    networks:
      - application

volumes:
  postgres_data:
  redis_data:
  opensearch_data:

networks:
  application:
