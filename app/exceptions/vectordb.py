from app.exceptions.base import (
    BadRequestBaseException,
    InternalServerBaseException,
)

vector_index_not_created = InternalServerBaseException(
    detail="could not create vector database"
)
vectordb_does_not_exists = BadRequestBaseException(
    detail="there is no index for provided namespace"
)

vectordb_not_connected = InternalServerBaseException(
    detail="connection to vectordb could not be established",
)

documents_not_indexed = InternalServerBaseException(
    detail="could not index documents in vector database",
)
