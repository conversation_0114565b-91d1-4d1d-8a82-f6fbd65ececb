from app.exceptions.base import BadRequestBaseException, UnAuthorizedBaseException

user_not_found = BadRequestBaseException(
    detail="User not found. Please register to create an account.",
)
invalid_user_credentials = UnAuthorizedBaseException(
    detail="Invalid user credentials provided. Authentication failed."
)

insufficient_permissions = UnAuthorizedBaseException(
    detail="You do not have sufficient permissions to perform this action."
)
