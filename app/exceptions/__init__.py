"""
Exception Module

This module provides base exception classes and pre-defined exceptions for common HTTP error scenarios.

Usage:
- Extend the base exception classes to create custom exceptions tailored to specific application needs.
- Import and raise the pre-defined exceptions for common HTTP error responses.
- Standardizes error responses across the application.
- Encourages structured exception handling for better error management.
"""
