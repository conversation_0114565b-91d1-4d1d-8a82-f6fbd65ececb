"""
    BASE EXCEPTION FILE
"""
from typing import Any, Dict, Optional

from fastapi import HTTPEx<PERSON>, status


class BaseHttpException(HTTPException):
    """Base exception class for custom HTTP exceptions."""

    def __init__(
        self,
        status_code: int,
        detail: Any,
        error_code: str = "base exception",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.error_code = error_code
        super().__init__(status_code=status_code, detail=detail, headers=headers)


class ModelNotFoundBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "not_found",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class UnAuthorizedBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "unauthorized",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class ForbiddenBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "forbidden",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class InternalServerBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "internal_server_error",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class BadRequestBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "bad_request",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


class ConflictBaseException(BaseHttpException):
    def __init__(
        self,
        detail: Any,
        error_code: str = "conflict",
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code=error_code,
            headers=headers,
        )


internal_server_error = InternalServerBaseException(
    detail="there was some error in processing your request.",
)

invalid_token = UnAuthorizedBaseException(detail="Invalid token provided.")

expired_token = UnAuthorizedBaseException(
    detail="Expired token provided. Please refresh your token for continued access.",
)
