# import tempfile
# from typing import Optional, Annotated, List

# from fastapi import UploadFile, Form, APIRouter, status, Depends

# # from app.api.dependencies import get_vector_database
# from app.services.s3_service import s3_service
# from loguru import logger
# # from app.exceptions.vectordb import documents_not_indexed
# from app.parsers.file import FileProcessor
# # from app.vectorstore.opensearch import OpenSearchVectorDB

# router = APIRouter()


# @router.post("/files/ingest", status_code=status.HTTP_201_CREATED)
# def ingest_file(
#     file: UploadFile,
#     vector_db: Annotated[OpenSearchVectorDB, Depends(get_vector_database)],
#     chunk_size: Optional[int] = Form(default=500),
#     chunk_overlap: Optional[int] = Form(default=50),
# ):
#     """
#     Ingest a file for document processing and vector storage.
#     """
#     try:
#         with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
#             tmp_file.write(file.file.read())

#             file_processor = FileProcessor(
#                 file=tmp_file,
#                 file_name=file.filename,
#                 chunk_size=chunk_size,
#                 chunk_overlap=chunk_overlap,
#             )
#             documents = file_processor.create_documents()

#         response = vector_db.add_documents(documents)
#         logger.info(f"Successfully ingested file {file.filename}")
#         return response
#     except Exception as exc:
#         logger.error(f"Error in ingesting file: {exc}")
#         raise documents_not_indexed from exc


# @router.post("/files/upload-and-ingest", status_code=status.HTTP_201_CREATED)
# def upload_and_ingest_file(
#     file: UploadFile,
#     bucket_name: str = Form(...),
#     folder_prefix: str = Form(default=""),
#     vector_db: Annotated[OpenSearchVectorDB, Depends(get_vector_database)] = None,
#     chunk_size: Optional[int] = Form(default=500),
#     chunk_overlap: Optional[int] = Form(default=50),
# ):
#     """
#     Upload a file to S3 and optionally ingest it for document processing.
#     """
#     try:
#         # Upload file to S3
#         upload_result = s3_service.upload_file(
#             file=file,
#             bucket_name=bucket_name,
#             folder_prefix=folder_prefix
#         )
        
#         result = {
#             "upload": upload_result,
#             "ingestion": None
#         }
        
#         # Optionally ingest for vector storage if vector_db is provided
#         if vector_db:
#             with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
#                 tmp_file.write(file.file.read())

#                 file_processor = FileProcessor(
#                     file=tmp_file,
#                     file_name=file.filename,
#                     chunk_size=chunk_size,
#                     chunk_overlap=chunk_overlap,
#                 )
#                 documents = file_processor.create_documents()

#             ingestion_result = vector_db.add_documents(documents)
#             result["ingestion"] = ingestion_result
#             logger.info(f"Successfully uploaded and ingested file {file.filename}")
#         else:
#             logger.info(f"Successfully uploaded file {file.filename} to S3")
        
#         return result
        
#     except Exception as exc:
#         logger.error(f"Error in upload and ingest: {exc}")
#         raise documents_not_indexed from exc
