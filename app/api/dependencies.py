"""
Dependencies:
- Dependencies are reusable functions that can be injected into endpoint functions.
- Use dependencies for tasks like database connections, service initialization, and more.
"""
from fastapi import Query, Depends
from loguru import logger

# from app.vectorstore.base import CustomBaseVectorStore
# from app.vectorstore.opensearch import vector_db


# def get_vector_database(namespace: Optional[str] = None) -> Type[CustomBaseVectorStore]:
#     if namespace:
#         # NOTE: add logic for using the namespaced index
#         pass
#     return vector_db


class PaginationParams:
    def __init__(
        self,
        page: int = Query(default=1, ge=1, description="current page number"),
        page_size: int = Query(
            default=10, ge=1, le=100, description="number of items per page"
        ),
    ) -> None:
        self.page = page
        self.page_size = page_size

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.page_size

    @property
    def limit(self) -> int:
        return self.page_size
