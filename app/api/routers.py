"""
Routers:
- Routers can be used to organize related endpoints into separate groups for better code organization.
- Routers can be mounted on the main application using `app.include_router(router, prefix="/prefix")`.
"""

from fastapi import APIRouter

from app.api.endpoints import health, s3

api_router = APIRouter()

api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(s3.router, prefix="/s3", tags=["s3"])
# api_router.include_router(ingestion.router, tags=["ingestion"])
