from abc import ABC, abstractmethod
from typing import List

from langchain.docstore.document import Document


class CustomBaseVectorStore(ABC):
    """Abstract base class for the Vector Database Wrapper Interface.
    This abstract base class defines the common interface and functionality
    required for working with vector databases. Concrete subclasses for
    specific vector databases should implement the methods defined here.
    """

    @abstractmethod
    def add_documents(self, docs: List[Document], **kwargs):
        """Insert documents into the vector database."""
        raise NotImplementedError("Subclasses must implement the 'insert_documents'.")

    @abstractmethod
    def similarity_search(self, **kwargs) -> List[Document]:
        """Search the vector database and retrieve matching documents."""
        raise NotImplementedError("Subclasses must implement the 'search_documents'.")

    @abstractmethod
    def delete_documents(self, docs: List[Document], **kwargs):
        """Delete one or more documents from the vector database."""
        raise NotImplementedError("Subclasses must implement the 'delete_documents'.")

    @abstractmethod
    def update_documents(self, docs: List[Document], **kwargs):
        """Update one or more documents in the vector database."""
        raise NotImplementedError("Subclasses must implement the 'update_documents'.")

    @abstractmethod
    def pre_filter_documents(self, docs: List[Document], **kwargs):
        """Rerank  documents in the vector database."""
        raise NotImplementedError(
            "Subclasses must implement the 'pre_filter_documents'"
        )

    @abstractmethod
    def post_filter_documents(self, docs: List[Document], **kwargs):
        """post filter documents in the vector database."""
        raise NotImplementedError(
            "Subclasses must implement the 'post_filter_documents'"
        )

    @abstractmethod
    def check_duplication(self, docs: List[Document], **kwargs):
        """code to check for duplication in vector database"""
        raise NotImplementedError("Subclasses must implement the 'check_duplication'")
