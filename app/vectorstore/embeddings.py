# import boto3
# from langchain_community.embeddings import BedrockEmbeddings
# from langchain_openai.embeddings import AzureOpenAIEmbeddings, OpenAIEmbeddings

# from app.core.configuration import settings


# class EmbeddingFactory:
#     @staticmethod
#     def get_openai_embedding_model():
#         return OpenAIEmbeddings(model="text-embedding-ada-002")

#     @staticmethod
#     def _get_titan_embedding_model():
#         bedrock_client = boto3.client(
#             service_name="bedrock-runtime", region_name="us-east-1"
#         )
#         bedrock_embeddings = BedrockEmbeddings(
#             model_id="amazon.titan-embed-text-v1", client=bedrock_client
#         )
#         return bedrock_embeddings

#     @staticmethod
#     def _get_azure_embedding_model():
#         return AzureOpenAIEmbeddings(
#             model="text-embedding-ada-002",
#             openai_api_key=settings.AZURE_OPENAI_API_KEY,
#             openai_api_version=settings.AZURE_OPENAI_DEPLOYMENT_VERSION,
#             azure_endpoint=settings.AZURE_OPENAI_DEPLOYMENT_ENDPOINT,
#             deployment=settings.AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
#         )

#     __embedding_mappings = {
#         "titan": _get_titan_embedding_model,
#         "azure-text-embedding-ada-002": _get_azure_embedding_model,
#         "text-embedding-ada-002": get_openai_embedding_model,
#     }

#     @classmethod
#     def supported_models(cls) -> tuple:
#         return tuple(cls.__embedding_mappings.keys())

#     @classmethod
#     def get_from_model_id(cls, model_id: str):
#         model_loader = cls.__embedding_mappings.get(model_id)

#         if model_loader is None:
#             raise ValueError(
#                 f"Model {model_id} is not supported. only {cls.supported_models()} supported right now"
#             )

#         return model_loader()


# embedding_model = EmbeddingFactory.get_from_model_id("titan")

# if __name__ == "__main__":
#     test_embedding = embedding_model.embed_query("hello, how are you!")
#     print(test_embedding)
