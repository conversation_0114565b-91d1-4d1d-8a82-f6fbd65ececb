# from typing import List, Tu<PERSON>, Dict, Any

# from langchain_community.vectorstores.opensearch_vector_search import (
#     OpenSearchVectorSearch,
# )
# from langchain_core.documents import Document
# from langchain_core.embeddings import Embeddings
# from opensearchpy import OpenSearch

# from app.core.configuration import settings
# from loguru import logger
# from app.vectorstore.base import CustomBaseVectorStore
# from app.vectorstore.embeddings import embedding_model


# class OpenSearchVectorDB(OpenSearchVectorSearch, CustomBaseVectorStore):
#     def __init__(
#         self,
#         opensearch_url: str,
#         index_name: str,
#         embedding_function: Embeddings,
#         http_auth: Tuple[str, str] = ("admin", "admin"),
#         **kwargs,
#     ):
#         self.client = OpenSearch(hosts=[opensearch_url], http_auth=http_auth, **kwargs)

#         super().__init__(
#             opensearch_url,
#             index_name,
#             embedding_function,
#             http_auth=http_auth,
#             **kwargs,
#         )

#     def add_documents(
#         self, documents: List[Document], check_duplicate=False, **kwargs: Any
#     ) -> List[str]:
#         # check for deduplication
#         if not documents:
#             logger.info("empty documents list was uploaded")
#             raise ValueError("empty documents list was uploaded")

#         if check_duplicate and self.check_duplication(documents):
#             logger.info(
#                 f"Document {documents[0].metadata.get('source')} already exists."
#             )
#             return []

#         return super().add_documents(documents, **kwargs)

#     def check_duplication(self, documents, **kwargs):
#         file_name = documents[0].metadata.get("source")
#         query = {
#             "query": {
#                 "term": {
#                     "source.keyword": file_name,
#                 }
#             }
#         }
#         count = self.client.count(
#             index=settings.OPENSEARCH_VECTOR_INDEX, body=query
#         ).get("count")

#         return count > 0

#     def similarity_search(self, query: str, **kwargs):
#         return super().similarity_search(query, **kwargs)

#     def update_documents(self, document_ids: List, updated_data: Dict, **kwargs):
#         bulk_update_request = []
#         for doc_id in document_ids:
#             bulk_update_request.extend(
#                 [
#                     {
#                         "update": {
#                             "_id": doc_id,
#                             "_index": settings.OPENSEARCH_VECTOR_INDEX,
#                         }
#                     },
#                     {"doc": updated_data},
#                 ]
#             )

#         # Perform the bulk delete operation
#         opensearch_response = self.client.bulk(
#             body=bulk_update_request, index=settings.OPENSEARCH_VECTOR_INDEX
#         )

#         # Check the response
#         if opensearch_response["errors"]:
#             for item in opensearch_response["items"]:
#                 if "update" in item and item["update"]["result"] != "updated":
#                     logger.exception(f"Document {item['update']['_id']} not updated")
#         else:
#             logger.info(f"Bulk update successful for {len(document_ids)} documents")

#     def delete_documents(self, document_ids: List, **kwargs):
#         """
#         The `delete_file_documents_bulk` function performs a bulk delete operation on a specified list of
#         document IDs in an OpenSearch index.

#         :param document_ids: A list of document IDs that need to be deleted
#         :type document_ids: List[str]
#         """
#         bulk_delete_request = []
#         for doc_id in document_ids:
#             bulk_delete_request.extend(
#                 [
#                     {
#                         "delete": {
#                             "_index": settings.OPENSEARCH_VECTOR_INDEX,
#                             "_id": doc_id,
#                         }
#                     },
#                 ]
#             )

#         # Perform the bulk delete operation
#         opensearch_response = self.client.bulk(
#             body=bulk_delete_request, index=settings.OPENSEARCH_VECTOR_INDEX
#         )

#         # Check the response
#         if opensearch_response["errors"]:
#             for item in opensearch_response["items"]:
#                 if "delete" in item and item["delete"]["result"] != "deleted":
#                     logger.exception(f"Document {item['delete']['_id']} not deleted")
#         else:
#             logger.info(f"Bulk delete successful for {len(document_ids)} documents")

#     def pre_filter_documents(self, docs: List[Document], **kwargs):
#         """Rerank  documents in the vector database."""
#         pass

#     def post_filter_documents(self, docs: List[Document], **kwargs):
#         """post filter documents in opensearch vector database."""
#         pass


# vector_db = OpenSearchVectorDB(
#     opensearch_url=settings.OPENSEARCH_URL,
#     index_name=settings.OPENSEARCH_VECTOR_INDEX,
#     embedding_function=embedding_model,
#     http_auth=(settings.OPENSEARCH_USERNAME, settings.OPENSEARCH_PASSWORD),
#     timeout=30,  # fix for opensearch read connection issues
#     max_retries=3,
#     retry_on_timeout=True,
# )
