from typing import List

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document


def custom_split_documents(
    documents: List[Document], chunk_size: int, chunk_overlap: int
) -> List[Document]:
    """
    Split a list of documents into smaller chunks for processing.

    This function splits a list of documents into smaller chunks based on the specified 'chunk_size'
    and 'chunk_overlap' parameters. It uses a text splitter to perform the split operation.

    Args:
        documents (List[Document]): A list of documents to be split.
        chunk_size (int): The desired size of each chunk.
        chunk_overlap (int): The number of overlapping characters between adjacent chunks.

    Returns:
        List[Document]: A list of structured documents representing the split content.

    Note:
        The 'chunk_size' and 'chunk_overlap' parameters control the granularity of the split.
    """

    text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        encoding_name="cl100k_base",
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
    )
    documents = text_splitter.split_documents(documents)

    return documents
