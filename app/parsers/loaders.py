import os
from typing import Dict, Any

from langchain_community.document_loaders import UnstructuredFileLoader


class CustomUnstructuredFileLoader(UnstructuredFileLoader):
    """
    Extends UnstructuredFileLoader to include metadata in parsed content.

    This class is an extension of the UnstructuredFileLoader and adds the ability to include
    metadata in the parsed content. It overrides the '_get_metadata' method to incorporate
    additional metadata from the 'metadata_dict' parameter.

    Args:
        file_path (str): The path to the file to be loaded.
        metadata_dict (Dict[str, str]): Additional metadata to be included in the parsed content.
        **unstructured_kwargs: Additional keyword arguments for UnstructuredFileLoader.

    """

    def __init__(
        self, file_path: str, metadata_dict: Dict[str, str], **unstructured_kwargs: Any
    ):
        super().__init__(file_path, **unstructured_kwargs)
        self.metadata_dict = metadata_dict or {}

    # override the metadata creation function
    def _get_metadata(self) -> dict:
        return {
            "source": os.path.basename(self.file_path).split(".")[0],
            **self.metadata_dict,
        }  # Merge metadata_dict using dictionary unpacking
