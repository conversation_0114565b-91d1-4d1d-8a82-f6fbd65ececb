import os
from typing import Optional, List, Any

from langchain_core.documents import Document
from pydantic import BaseModel, field_validator, Field

from loguru import logger
from app.parsers.chunking import custom_split_documents
from app.parsers.loaders import CustomUnstructuredFileLoader


class FileProcessor(BaseModel):
    file: Any
    file_name: str
    file_extension: Optional[str] = Field(default=None)
    chunk_size: int = Field(
        default=500, description="chunk size for splitting the documents"
    )
    chunk_overlap: int = Field(default=50, ge=0, description="overlap between chunks")
    content: Optional[bytes] = Field(default=None)
    documents: Optional[List[Document]] = Field(default=[])

    class Config:
        arbitrary_types_allowed = True

    @field_validator("file_name")
    def validate_uploaded_file(cls, value):
        # remove directory path from file name to prevent error and future changes
        file_name = os.path.basename(value)
        return file_name

    @field_validator("file_extension")
    def validate_file_extension(cls, value, values):
        if not value:
            value = os.path.splitext(values["file_name"])[-1]

        return value.lower()

    @property
    def is_empty(self):
        """
        Check if file is empty by checking if the file pointer is at the beginning of the file
        if tell method returns value = 0 then fil is empty
        """
        return self.file.tell() == 0

    def create_documents(self) -> List[Document]:
        try:
            with open(self.file.name, "rb") as file:
                file.seek(0)
                #NOTE: update the metadata dict
                metadata_dict = {"source": self.file_name}
                loader = CustomUnstructuredFileLoader(file_path=self.file.name, metadata_dict=metadata_dict)
                self.documents = loader.load()
                self.documents = custom_split_documents(
                    self.documents, self.chunk_size, self.chunk_overlap
                )

        except Exception as exc:
            logger.exception(f"Error in creating documents: {exc}")
            raise exc

        finally:
            # finally remove the temp file from os
            os.remove(self.file.name)

        return self.documents
