import time

from fastapi.requests import Request
from fastapi.responses import Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from loguru import logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging HTTP requests and responses.

    This middleware intercepts incoming HTTP requests, logs request details, delegates processing to the next middleware
    in the stack, logs the response details, and then returns the response.

    Args:
        BaseHTTPMiddleware: Base class for HTTP middleware in Starlette.

    Methods:
        dispatch: Processes the incoming request, logs request and response details, and returns the response.
        _get_request_log: Retrieves and formats request details for logging.
        set_body: Sets the request body for logging purposes within the middleware.
        _execute_and_log_response: Executes the request processing and logs response details.
    """

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Processes the incoming request, logs request and response details, and returns the response.

        Args:
            request (Request): The incoming HTTP request.
            call_next (RequestResponseEndpoint): The endpoint to call to proceed with request processing.

        Returns:
            Response: The HTTP response generated by the application.
        """

        response, response_log = await self._execute_and_log_response(
            request, call_next
        )
        request_log = await self._get_request_log(request)
        log_dict = {
            "request": request_log,
            "response": response_log,
            "correlation_id": request.headers.get("x-request-id", ""),
        }
        logger.info("logged from middleware", extra={"extra_params": log_dict})

        return response

    @staticmethod
    async def _get_request_log(request: Request):
        """
        Retrieves and formats request details for logging.

        Args:
            request (Request): The incoming HTTP request.

        Returns:
            dict: A dictionary containing request details formatted for logging.
        """

        try:
            body = await request.json()
        except Exception:
            body = None

        return {
            "method": request.method,
            "url": request.url.path,
            "ip": request.client.host,
            "query_params": str(request.query_params),
            "body": body,
            "headers": request.headers,
        }

    @staticmethod
    async def _execute_and_log_response(
        request: Request, call_next: RequestResponseEndpoint
    ):
        """
        Executes the request processing and logs response details.

        Args:
            request (Request): The incoming HTTP request.
            call_next (RequestResponseEndpoint): The endpoint to call to proceed with request processing.

        Returns:
            tuple: A tuple containing the HTTP response generated by the application and response details formatted for logging.
        """
        start_time = time.perf_counter()
        response = await call_next(request)
        end_time = time.perf_counter()

        response_log = {
            "status_code": response.status_code,
            "response_time": end_time - start_time,
        }

        return response, response_log
