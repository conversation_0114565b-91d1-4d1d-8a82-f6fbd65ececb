"""
Main entry point for the FastAPI application.
"""
from contextlib import asynccontextmanager
from uuid import uuid4

import uvicorn
from asgi_correlation_id import CorrelationIdMiddleware, correlation_id
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api.routers import api_router
from app.core.configuration import settings
from loguru import logger
from app.exceptions.base import BaseHttpException
from app.middleware.request_logging import LoggingMiddleware
from app.schemas.base import BaseExceptionResponse


@asynccontextmanager
async def lifespan(_: FastAPI):
    # before loading fastapi application
    yield
    # Clean up after closing fastapi application
    logger.info("shutting down")


app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url="/openapi.json" if settings.SHOW_SWAGGER_DOC else None,
    lifespan=lifespan,
    debug=False,
)


@app.exception_handler(Exception)
async def unhandled_exception_handler(request: Request, exc: Exception):
    # log the error and raise internal server exception
    logger.error(
        f"unhandled exception {exc} ",
        exc_info=True,
        extra={
            "extra_params": {"correlation_id": request.headers.get("x-request-id", "")}
        },
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "ok": False,
            "error_code": "internal_server_error",
            "detail": "oops!, something went wrong on our side please try again.",
        },
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(
        "unhandled request validation error",
        exc_info=True,
        extra={
            "extra_params": {"correlation_id": request.headers.get("x-request-id", "")}
        },
    )

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": exc.errors(), "body": exc.body}),
    )


@app.exception_handler(HTTPException)
async def business_exception_handler(
    request: Request, exc: BaseHttpException
) -> JSONResponse:
    """
    Custom exception handler for HTTPException
    """
    try:
        exc_details = BaseExceptionResponse(
            ok=False, error_code=exc.error_code, detail=exc.detail
        )
        return JSONResponse(status_code=exc.status_code, content=exc_details.dict())
    except Exception as exc:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "request": str(request),
                "message": str(exc),
                "correlation_id": correlation_id.get(),
            },
        )


app.add_middleware(
    CorrelationIdMiddleware,
    header_name="X-Request-ID",
    update_request_header=True,
    generator=lambda: uuid4().hex,
    transformer=lambda a: a,
)

app.add_middleware(LoggingMiddleware)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)

if __name__ == "__main__":
    uvicorn.run("app.main:app", host=settings.HOST, port=settings.PORT)
