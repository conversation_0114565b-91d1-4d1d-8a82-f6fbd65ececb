from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate,SystemMessagePromptTemplate
import base64

template = """
You are a very intelligent chatbot whose goal is to provide the summary of given image.
"""


async def get_formatted_prompt(question,images):
    content = [{
                    "type": "text",
                    "text": f"{question}",
                },
            ]

    for img in images:
        image_bytes = await img.read()
        image_data = base64.b64encode(image_bytes).decode("utf-8")
        content.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_data}"},
                    })

    multimodal_prompt = ChatPromptTemplate(
        messages=[
            SystemMessagePromptTemplate.from_template(template),
            HumanMessage(
                content=content
            ),
        ]
    )

    return multimodal_prompt