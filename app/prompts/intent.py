from langchain_core.prompts import ChatPromptTemplate

# Precision score with dynamic calculation
template = """
Please classify the question based on its intent and generate_response a [precision score] ranging from 0 to 1, representing the relevance of the given question to the matched intent.
Calculate the precision score based on factors like keyword matching, context relevance, and similarity. Higher scores should indicate a stronger alignment between the question and intent.
YOU SHOULD ALWAYS ANSWER WITH A VALID JSON OBJECT ACCORDING TO THE SCHEMA SPECIFIED FOR THAT INTENT. please DO NOT add any additional information
or answer the question as it will break the flow of the application which is based on a valid JSON response.

Below are some of the examples based on which you have to classify the intent and provide JSON response in its corresponding
schema.You have to strictly adhere to the JSON schemas for respective intent and do not add or remove anything from the response schema.

1. [SUMMARY]
  Description: Summarize articles or topics.
 - Example 1: "Summarize all articles in the Journal of [name] related to [topic]." 
 - Example 2: "Provide a summary of [topic]." 
 - Example 3: "Summarize the article "[topic]" from the Journal of Medical Research." 

 Output Format (JSON):
  {{
  "intent": "SUMMARY",
  "score": [precision score]
  }}

2. [SEARCH]
 Description: Provide a specific information or details about a particular generic topic. this is a generic intent and 
 if any of the below mentioned intents are relevant to user's question then give them a higher priority.

 - Example 1: "what are the effects of social media on self-esteem and body image."
 - Example 2: "explain the impact of childhood trauma on adult mental health and well-being."
 - Example 3: "Does parent's emotional regulation impact aggression in children?"

 Output Format (JSON):
  {{
  "intent": "SEARCH",
  "score": [precision score]
  }}

3. [COMPARE TOPIC]
 Description: Compare two topics or subjects and generate_response a precision score.
 - Example 1: "Compare cognitive-behavioral therapy and psychodynamic therapy for treating anxiety disorders." 

 Output (JSON):
  {{
  "intent": "COMPARE TOPIC",
  "score": [precision score],
    "metadata": {{
    "question1": "Explain cognitive-behavioral therapy for treating anxiety disorders",
    "question2": "Explain psychodynamic therapy for treating anxiety disorders"
    }}
  }}

 - Example 2: "Analyze major depressive disorder and bipolar disorder in terms of symptoms and treatment approaches." 

 Output (JSON):
  {{
  "intent": "COMPARE TOPIC",
  "score": [precision score],
    "metadata": {{
    "question1": "Explain major depressive disorder in terms of symptoms and treatment approaches",
    "question2": "explain major bipolar disorder in terms of symptoms and treatment approaches"
    }}
  }}

 - Example 3: "Compare the fundamental principles and methodologies of cognitive psychology and behavioral psychology, highlighting their distinct approaches to understanding human behavior and mental processes." 

 Output (JSON):
  {{
  "intent": "COMPARE TOPIC",
  "score": [precision score],
    "metadata": {{
    "question1": "explain fundamental principles and methodologies of cognitive psychology highlighting its distinct approaches to understanding human behavior and mental processes",
    "question2": "explain fundamental principles and methodologies of behavioral psychology highlighting its distinct approaches to understanding human behavior and mental processes"
    }}
  }}


4. [COMPARE AUTHOR]
 Description: Compare the works of two authors.
 - Example 1: "Compare the works of author Christina Salmivalli and Lauren Henry in the field of depression." 

 Output (JSON):
  {{
  "intent": "COMPARE AUTHOR",
  "score": [precision score],
    "metadata": {{
    "author1": "Christina Salmivalli",
    "question1": "Provide information about works of Christina Salmivalli in the field of depression",
    "author2": "Lauren Henry",
    "question2": "provide information about works of Lauren Henry in the field of depression"
    }}
  }}

 - Example 2: "provide the comparison of the works of john smith and emily johnson on the topic of Artificial intelligence in clinical science. 

 Output (JSON):
  {{
  "intent": "COMPARE AUTHOR",
  "score": [precision score],
    "metadata": {{
    "author1": "john smith",
    "question1": "provide information about works of john smith on the topic of artificial intelligence in clinical science.",
    "author2": "emily johnson",
    "question2": "provide information about works of emily johnson on the topic of artificial intelligence in clinical science."
    }}
  }}

5. [FOLLOW-UP QUESTION]
 Description: provide an answer to a question with reference to previous conversation history i.e asking a
 follow-up question to the past conversations. follow up questions are not independent on it's own
 and makes a reference to past conversations.
 if a main subject is missing and the question makes more sense as follow up to a previous question,
 then this is supposed to be intent of the question.

- Example 1: "what was the aim of this study"
- Example 2: "what are the implications of above on stressors for minorities"
- Example 3: "What  are the  main cause of problem for children self-view?"
- Example 4: "what could be the possible reasons for those conditions in cognitive schema formations"
- Example 5: "can I use it to measure dispositional persistence?"
- Example 6: "can you provide the difference between the topics mentioned above"
- Example 7: "how is that shaping up the research in field of gender studies"
- Example 8: "provide me the summary of my previous conversations"
- Example 9: "what evidences are discovered by above studies on sexual disparities?"
- Example 10: "according to that answer what type of children experienced less global positive affect"

  Output Format (JSON):
  {{
  "intent": "FOLLOW-UP QUESTION",
  "score": [precision score]
  }}

ANSWER: (VALID JSON OBJECT)
{{
 "intent":
"""

INTENT_PROMPT = ChatPromptTemplate.from_messages([
        ("system", template),
        ("human", "{question}")
    ]
)
