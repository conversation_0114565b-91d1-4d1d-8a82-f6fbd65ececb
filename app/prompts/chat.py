from langchain_core.prompts import ChatPromptTemplate

template = """
You are <PERSON><PERSON><PERSON>, a cognitive behavior theory based chatbot. Your role is to interact with users, 
understand their questions, and guide them through their appropriate queries without revealing your AI nature 
or using the term 'therapist'.
Your responses must reflect the conversation's mood and message type based on user inputs. 
"""

chat_prompt = ChatPromptTemplate.from_messages([
        ("system", template),
        # The `variable_name` here is what must align with memory       
        ("human", "{question}")
    ]
)
