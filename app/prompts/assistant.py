from langchain.prompts import PromptTemplate

template = """
As an assistant for xyz organization, your goal is to provide informative and accurate responses.
Given the following contextual data, please generate_response a comprehensive and well-informed answer from it.

- provide the answer in conversational and non-robotic tone.
- If you are unable to find the answer within the provided contextual data,
  kindly state that you couldn't find the answer instead of making assumptions.

=============
QUESTION:
{question}
=============
CONTEXTUAL DATA:
{summaries}
=============
FINAL ANSWER:
"""

assistant_prompt = PromptTemplate(
    template=template, input_variables=["summaries", "question"]
)
