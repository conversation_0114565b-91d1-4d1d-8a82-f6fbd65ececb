from typing import Optional

from langchain_openai import ChatOpenAI

from app.core.configuration import settings
from loguru import logger
from app.llm.base import ModelConfigBase


class OpenAIModelConfig(ModelConfigBase):
    def __init__(
        self,
        model_id: str,
        max_token_limit: int,
        answer_token: int,
        memory_token: int,
    ):
        model = self._initialize_model(
            answer_token=answer_token,
            model_id=model_id,
        )

        super().__init__(
            model_id=model_id,
            model=model,
            max_token_limit=max_token_limit,
            answer_token=answer_token,
            memory_token=memory_token,
        )

    def _initialize_model(
        self,
        answer_token: int,
        model_id: str = "gpt-3.5-turbo",
        temperature: float = 0.5,
        streaming: bool = True,
    ) -> ChatOpenAI:
        try:
            openai_api_key = settings.OPENAI_API_KEY

            return ChatOpenAI(
                model=model_id,
                temperature=temperature,
                openai_api_key=openai_api_key,
                max_tokens=answer_token,
                streaming=streaming,
            )

        except Exception as exc:
            logger.warning(f"error in initializing {model_id} \n {str(exc)}")

    @classmethod
    def create_from_model_config(
        cls,
        model_id: str,
        max_token_limit: int,
        answer_token: int,
        memory_token: Optional[int] = 0,
    ) -> "OpenAIModelConfig":
        return cls(
            model_id=model_id,
            max_token_limit=max_token_limit,
            answer_token=answer_token,
            memory_token=memory_token,
        )


gpt_35_turbo = OpenAIModelConfig.create_from_model_config(
    model_id="gpt-3.5-turbo",
    max_token_limit=4096,
    answer_token=350,
    memory_token=400,
)
