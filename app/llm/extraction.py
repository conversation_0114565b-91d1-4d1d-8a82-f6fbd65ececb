"""
Production-ready document extraction service using AWS Textract and Bedrock Nova Pro.

This module provides a comprehensive document extraction pipeline that:
1. Uses AWS Textract for document analysis and OCR
2. Uses Bedrock Nova Pro model for intelligent data extraction
3. Processes documents directly from S3 using S3 keys
4. Includes comprehensive logging for AWS environments
5. Handles async operations for production scalability
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from app.core.configuration import settings
from app.utils.textract import analyze_document_with_textract
from app.llm.bedrock import extract_data_with_bedrock

# Configure logging for AWS environments
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # This will output to terminal/CloudWatch
    ]
)
logger = logging.getLogger(__name__)


class DocumentExtractionProcessor:
    """
    Production-ready document extraction processor using AWS Textract and Bedrock Nova Pro.

    This class handles the complete extraction pipeline:
    - Document analysis using AWS Textract
    - Intelligent data extraction using Bedrock Nova Pro
    - Error handling and retry logic
    - Comprehensive logging for production monitoring
    """

    def __init__(self, s3_client, aws_account_id: str, aws_region: Optional[str] = None,
                 aws_access_key_id: Optional[str] = None, aws_secret_access_key: Optional[str] = None):
        """
        Initialize the extraction processor.

        Args:
            s3_client: Pre-configured S3 client
            aws_account_id: AWS account ID
            aws_region: AWS region (defaults to settings)
            aws_access_key_id: AWS access key (defaults to settings)
            aws_secret_access_key: AWS secret key (defaults to settings)
        """
        logger.info("🚀 Initializing DocumentExtractionProcessor...")

        try:
            self.s3_client = s3_client
            self.aws_account_id = aws_account_id
            self.region = aws_region or settings.AWS_REGION
            self.aws_access_key_id = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
            self.aws_secret_access_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

            logger.info("✅ Successfully initialized DocumentExtractionProcessor")
            logger.info(f"   AWS Account ID: {self.aws_account_id}")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Timestamp: {datetime.now().isoformat()}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize DocumentExtractionProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    def extract_s3_info(self, s3_key: str, bucket_name: Optional[str] = None) -> tuple:
        """
        Extract bucket and key information from S3 key or URI.

        Args:
            s3_key: S3 key or full S3 URI
            bucket_name: Optional bucket name if s3_key is just the key

        Returns:
            tuple: (bucket_name, object_key)
        """
        logger.debug(f"📍 Extracting S3 info from: {s3_key}")

        if s3_key.startswith('s3://'):
            # Full S3 URI provided
            parts = s3_key[5:].split('/', 1)
            bucket = parts[0]
            key = parts[1] if len(parts) > 1 else ''
            logger.debug(f"   Extracted from URI - Bucket: {bucket}, Key: {key}")
            return bucket, key
        else:
            # Just the key provided, use provided bucket_name or default
            bucket = bucket_name or settings.S3_BUCKET_NAME
            if not bucket:
                raise ValueError("Bucket name must be provided either in s3_key URI or bucket_name parameter")
            logger.debug(f"   Using provided key - Bucket: {bucket}, Key: {s3_key}")
            return bucket, s3_key
    
    async def analyze_document_with_textract(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: Textract analysis results
        """
        return await analyze_document_with_textract(
            bucket_name, object_key,
            self.region, self.aws_access_key_id, self.aws_secret_access_key
        )
    
    async def extract_data_with_bedrock_nova(self, text_content: str,
                                           system_prompt: Optional[str] = None,
                                           user_prompt: Optional[str] = None,
                                           temperature: float = 0.1,
                                           max_tokens: int = 4000,
                                           top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model.

        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling

        Returns:
            dict: Extracted structured data
        """
        return await extract_data_with_bedrock(
            text_content, system_prompt, user_prompt, temperature, max_tokens, top_p,
            self.region, self.aws_access_key_id, self.aws_secret_access_key
        )



    async def process_document_extraction(self, s3_key: str, bucket_name: Optional[str] = None,
                                        extraction_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete document extraction pipeline: Textract + Bedrock Nova Pro.

        Args:
            s3_key: S3 key or full S3 URI of the document
            bucket_name: Optional bucket name if s3_key is just the key
            extraction_prompt: Optional custom extraction prompt for Bedrock

        Returns:
            dict: Complete extraction results including metadata
        """
        logger.info("🎯 Starting complete document extraction pipeline...")
        logger.info(f"   S3 Key: {s3_key}")
        logger.info(f"   Bucket: {bucket_name}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")
        logger.info("="*80)

        start_time = datetime.now()

        try:
            # Extract S3 information
            bucket, object_key = self.extract_s3_info(s3_key, bucket_name)

            # Step 1: Analyze document with Textract
            logger.info("📋 Step 1: Document analysis with Textract")
            textract_result = await self.analyze_document_with_textract(bucket, object_key)

            # Step 2: Extract text from Textract result
            logger.info("📋 Step 2: Text extraction from Textract results")
            extracted_text = textract_result.get('extracted_text', '')

            # Step 3: Extract structured data with Bedrock Nova Pro
            logger.info("📋 Step 3: Structured data extraction with Bedrock Nova Pro")
            bedrock_result = await self.extract_data_with_bedrock_nova(
                extracted_text, user_prompt=extraction_prompt
            )

            # Compile final results
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            final_result = {
                "extraction_metadata": {
                    "s3_location": f"s3://{bucket}/{object_key}",
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "textract_blocks_count": len(textract_result.get('textract_raw_result', {}).get('Blocks', [])),
                    "extracted_text_length": len(extracted_text),
                    "aws_region": self.region,
                    "aws_account_id": self.aws_account_id
                },
                "textract_raw_result": textract_result.get('textract_raw_result', {}),
                "extracted_text": extracted_text,
                "structured_data": bedrock_result.get('structured_data', {})
            }

            logger.info("🎉 Document extraction pipeline completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")
            logger.info(f"   Final result keys: {list(final_result.keys())}")
            logger.info(f"   Completion timestamp: {end_time.isoformat()}")
            logger.info("="*80)

            return final_result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Document extraction pipeline failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            logger.error("="*80)
            raise


# Utility functions for production use
async def extract_document_data(s3_key: str, s3_client, aws_account_id: str,
                              bucket_name: Optional[str] = None,
                              extraction_prompt: Optional[str] = None,
                              aws_region: Optional[str] = None,
                              aws_access_key_id: Optional[str] = None,
                              aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Main entry point for document extraction.

    This is the primary function to be called for document extraction in production.

    Args:
        s3_key: S3 key or full S3 URI of the document to process
        s3_client: Pre-configured S3 client
        aws_account_id: AWS account ID
        bucket_name: Optional bucket name if s3_key is just the key
        extraction_prompt: Optional custom prompt for data extraction
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Complete extraction results

    Example:
        # Using S3 URI
        result = await extract_document_data('s3://my-bucket/documents/invoice.pdf', s3_client, account_id)

        # Using S3 key with bucket name
        result = await extract_document_data('documents/invoice.pdf', s3_client, account_id, 'my-bucket')
    """
    logger.info("🚀 Starting document extraction process...")

    try:
        processor = DocumentExtractionProcessor(
            s3_client, aws_account_id, aws_region, aws_access_key_id, aws_secret_access_key
        )
        result = await processor.process_document_extraction(s3_key, bucket_name, extraction_prompt)

        logger.info("✅ Document extraction completed successfully")
        return result

    except Exception as e:
        logger.error(f"❌ Document extraction failed: {str(e)}")
        raise


def extract_key_fields(extraction_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract key business fields from the extraction result for easy access.

    Args:
        extraction_result: Result from extract_document_data function

    Returns:
        dict: Key business fields extracted from the document
    """
    logger.info("📊 Extracting key business fields...")

    try:
        structured_data = extraction_result.get('structured_data', {})

        # Common field mappings (adjust based on your specific needs)
        key_fields: Dict[str, Any] = {
            'vendor_name': None,
            'invoice_number': None,
            'invoice_date': None,
            'total_amount': None,
            'currency': None,
            'customer_name': None,
            'document_type': None
        }

        # Extract fields with various possible key names
        if isinstance(structured_data, dict):
            # Vendor/Company name variations
            for vendor_key in ['vendor_name', 'company_name', 'supplier_name', 'VENDORNAME', 'vendor', 'company']:
                if vendor_key in structured_data and structured_data[vendor_key]:
                    key_fields['vendor_name'] = structured_data[vendor_key]
                    break

            # Invoice number variations
            for invoice_key in ['invoice_number', 'document_id', 'ID', 'invoice_id', 'number']:
                if invoice_key in structured_data and structured_data[invoice_key]:
                    key_fields['invoice_number'] = structured_data[invoice_key]
                    break

            # Date variations
            for date_key in ['invoice_date', 'date', 'DATE', 'document_date', 'issue_date']:
                if date_key in structured_data and structured_data[date_key]:
                    key_fields['invoice_date'] = structured_data[date_key]
                    break

            # Total amount variations
            for total_key in ['total_amount', 'total', 'TOTAL', 'amount', 'grand_total']:
                if total_key in structured_data and structured_data[total_key]:
                    key_fields['total_amount'] = structured_data[total_key]
                    break

            # Currency variations
            for currency_key in ['currency', 'CURRENCY', 'currency_code']:
                if currency_key in structured_data and structured_data[currency_key]:
                    key_fields['currency'] = structured_data[currency_key]
                    break

            # Customer variations
            for customer_key in ['customer_name', 'customer', 'bill_to', 'client_name']:
                if customer_key in structured_data and structured_data[customer_key]:
                    key_fields['customer_name'] = structured_data[customer_key]
                    break

        # Add metadata
        key_fields['extraction_timestamp'] = datetime.now().isoformat()
        key_fields['source_document'] = extraction_result.get('extraction_metadata', {}).get('s3_location', 'unknown')

        logger.info("✅ Key fields extraction completed")
        logger.info(f"   Extracted fields: {[k for k, v in key_fields.items() if v is not None]}")

        return key_fields

    except Exception as e:
        logger.error(f"❌ Error extracting key fields: {str(e)}")
        logger.error(f"   Error type: {type(e).__name__}")
        raise


# Main function for testing and standalone execution
async def main(s3_key: str = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf',
               bucket_name: Optional[str] = None):
    """
    Main function for testing the extraction pipeline.

    Args:
        s3_key: S3 key or URI of the document to process
        bucket_name: Optional bucket name
    """
    import boto3

    logger.info("🎯 Starting Document Extraction Pipeline")
    logger.info(f"   Input S3 Key: {s3_key}")
    logger.info(f"   Bucket Name: {bucket_name}")
    logger.info(f"   Timestamp: {datetime.now().isoformat()}")
    logger.info("="*80)

    try:
        # Create required clients
        s3_client = boto3.client(
            's3',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        sts_client = boto3.client(
            'sts',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        aws_account_id = sts_client.get_caller_identity().get('Account')

        # Run the extraction
        result = await extract_document_data(s3_key, s3_client, aws_account_id, bucket_name)

        # Extract key fields for summary
        key_fields = extract_key_fields(result)

        # Log summary
        logger.info("\n📊 EXTRACTION SUMMARY:")
        logger.info(f"   Vendor Name: {key_fields.get('vendor_name', 'N/A')}")
        logger.info(f"   Invoice Number: {key_fields.get('invoice_number', 'N/A')}")
        logger.info(f"   Invoice Date: {key_fields.get('invoice_date', 'N/A')}")
        logger.info(f"   Total Amount: {key_fields.get('total_amount', 'N/A')}")
        logger.info(f"   Currency: {key_fields.get('currency', 'N/A')}")
        logger.info(f"   Customer: {key_fields.get('customer_name', 'N/A')}")
        logger.info(f"   Processing Time: {result.get('extraction_metadata', {}).get('total_processing_time_seconds', 'N/A')} seconds")

        return result

    except Exception as e:
        logger.error(f"\n❌ Pipeline failed with error: {str(e)}")
        logger.error(f"   Error type: {type(e).__name__}")
        logger.error(f"   Timestamp: {datetime.now().isoformat()}")
        raise


if __name__ == "__main__":
    # Example usage
    import sys

    # Get S3 key from command line argument or use default
    s3_key = sys.argv[1] if len(sys.argv) > 1 else 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'

    # Run the extraction pipeline
    asyncio.run(main(s3_key))
