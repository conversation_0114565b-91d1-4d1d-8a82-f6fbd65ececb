# Document Extraction Module

## Overview

The `extraction.py` module provides a production-ready document extraction service that combines AWS Textract for document analysis and Amazon Bedrock Nova Pro for intelligent data extraction. This module is designed for AWS environments with comprehensive logging for monitoring and debugging.

## Features

- **AWS Textract Integration**: OCR and document analysis for various document types
- **Bedrock Nova Pro**: Advanced AI-powered data extraction and structuring
- **S3 Direct Processing**: Processes documents directly from S3 using S3 keys (no file uploads)
- **Async Operations**: Fully asynchronous for production scalability
- **Comprehensive Logging**: Detailed logging for AWS CloudWatch integration
- **Error Handling**: Robust error handling with retry logic
- **Production Ready**: Designed for AWS production environments

## Usage

### Basic Usage

```python
import asyncio
from app.llm.extraction import extract_document_data

# Extract data from a document in S3
async def main():
    # Using full S3 URI
    result = await extract_document_data('s3://my-bucket/documents/invoice.pdf')
    
    # Using S3 key with bucket name
    result = await extract_document_data('documents/invoice.pdf', 'my-bucket')
    
    print(result)

asyncio.run(main())
```

### Command Line Usage

```bash
# Process a document using the default S3 URI
python app/llm/extraction.py

# Process a specific document
python app/llm/extraction.py s3://my-bucket/my-document.pdf
```

### Advanced Usage with Custom Prompt

```python
from app.llm.extraction import extract_document_data, extract_key_fields

async def extract_with_custom_prompt():
    custom_prompt = """
    Extract the following specific fields from this purchase order:
    - PO Number
    - Vendor Information
    - Line Items with quantities and prices
    - Delivery Date
    Return as JSON format.
    
    Document Text: {text}
    """
    
    result = await extract_document_data(
        's3://my-bucket/purchase-order.pdf',
        extraction_prompt=custom_prompt
    )
    
    # Extract key business fields
    key_fields = extract_key_fields(result)
    
    return result, key_fields
```

## Configuration

### Environment Variables

Ensure the following environment variables are set:

```bash
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-default-bucket  # Optional
```

### AWS Permissions

The AWS credentials must have permissions for:
- `textract:StartDocumentAnalysis`
- `textract:GetDocumentAnalysis`
- `bedrock:InvokeModel` (for Nova Pro model)
- `s3:GetObject` (for reading documents)
- `sts:GetCallerIdentity`

## Response Format

The extraction function returns a comprehensive result dictionary:

```json
{
  "extraction_metadata": {
    "s3_location": "s3://bucket/key",
    "processing_start_time": "2024-01-15T10:30:00",
    "processing_end_time": "2024-01-15T10:31:30",
    "total_processing_time_seconds": 90.5,
    "textract_blocks_count": 150,
    "extracted_text_length": 2500,
    "aws_region": "us-east-1",
    "aws_account_id": "************"
  },
  "textract_raw_result": { /* Full Textract response */ },
  "extracted_text": "Full text content from document...",
  "structured_data": {
    "vendor_name": "ABC Company",
    "invoice_number": "INV-12345",
    "total_amount": "1,250.00",
    "currency": "USD",
    /* ... other extracted fields ... */
  }
}
```

## Key Functions

### `extract_document_data(s3_key, bucket_name=None, extraction_prompt=None)`
Main entry point for document extraction.

### `extract_key_fields(extraction_result)`
Extracts common business fields from the extraction result.

### `DocumentExtractionProcessor`
Core class that handles the extraction pipeline.

## Logging

The module provides comprehensive logging at different levels:
- **INFO**: General processing information
- **DEBUG**: Detailed processing steps
- **ERROR**: Error conditions with context
- **WARNING**: Non-fatal issues

Logs include timestamps, processing times, and AWS resource information for production monitoring.

## Error Handling

The module handles various error conditions:
- AWS service errors (Textract, Bedrock, S3)
- Network timeouts and retries
- Invalid document formats
- Missing AWS permissions
- S3 access issues

## Performance Considerations

- **Async Operations**: All AWS calls are asynchronous
- **Polling Optimization**: Efficient polling for Textract job completion
- **Memory Management**: Streams large documents efficiently
- **Timeout Handling**: Configurable timeouts for long-running operations

## Dependencies

- `boto3`: AWS SDK
- `asyncio`: Async operations
- `json`: JSON processing
- `logging`: Comprehensive logging
- `datetime`: Timestamp handling
- `typing`: Type hints

## Testing

The module includes comprehensive error handling and logging for production use. Test with various document types to ensure proper extraction results.

## Support

For issues or questions:
1. Check AWS CloudWatch logs for detailed error information
2. Verify AWS permissions and credentials
3. Ensure S3 documents are accessible
4. Review extraction prompts for accuracy
