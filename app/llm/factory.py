from typing import Mapping, Any, Type

from app.llm import openai
from app.llm.azure_openai import gpt_35_turbo, gpt_35_turbo_16k
from app.llm.base import ModelConfigBase
from app.llm.bedrock import titan_express, claude_v2, claude_v3


class LLMFactory:
    __model_mappings: Mapping[str, Any] = {
        "gpt-3.5-turbo": openai.gpt_35_turbo,
        "gpt-3.5-turbo(azure)": gpt_35_turbo,
        "gpt-3.5-turbo-16k(azure)": gpt_35_turbo_16k,
        "anthropic-claude-v2": claude_v2,
        "anthropic-claude-v3": claude_v3,
        "amazon-titan-express": titan_express,
    }

    @classmethod
    def supported_models(cls) -> tuple:
        return tuple(cls.__model_mappings.keys())

    @classmethod
    def get_from_model_id(cls, model_id: str) -> Type[ModelConfigBase]:
        model = cls.__model_mappings.get(model_id)

        if model is None:
            raise ValueError(
                f"Model {model_id} is not supported. only {cls.supported_models()} supported right now"
            )

        return model
