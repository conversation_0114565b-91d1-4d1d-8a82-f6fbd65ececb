from typing import Optional

from langchain_openai import AzureChatOpenAI

from app.core.configuration import settings
from loguru import logger

from app.llm.base import ModelConfigBase


class AzureModelConfig(ModelConfigBase):
    """
    Configuration for Azure language models.

    Args:
        deployment_name (str): The name of the deployment.

        model_id (str): The name of the model.
        max_token_limit (int): The maximum number of tokens that can be processed by the model
        answer_token (int): The recommended token count for generating answers or responses.
    """

    def __init__(
        self,
        deployment_name: str,
        model_id: str,
        max_token_limit: int,
        answer_token: int,
        memory_token: int,
    ):
        model = self._initialize_model(
            deployment_name=deployment_name,
            answer_token=answer_token,
            model_id=model_id,
        )

        super().__init__(
            model_id=model_id,
            model=model,
            max_token_limit=max_token_limit,
            answer_token=answer_token,
            memory_token=memory_token,
        )

    def _initialize_model(
        self,
        deployment_name: str,
        answer_token: int,
        model_id: str = "gpt-3.5-turbo",
        temperature: float = 0.5,
        streaming: bool = True,
    ) -> AzureChatOpenAI:
        """
        Initialize an Azure language model.

        Args:
            deployment_name (str): The name of the deployment.
            answer_token (int): The recommended token count for generating answers or responses.
            model_id (str, optional): The name of the model (default is "gpt-3.5-turbo").
            temperature (float, optional): The temperature parameter for model generation (default is 0.5).
            streaming (bool, optional): Indicates whether to use streaming for responses (default is True).
        """

        try:
            openai_api_version = settings.AZURE_OPENAI_DEPLOYMENT_VERSION
            openai_api_key = settings.AZURE_OPENAI_API_KEY
            openai_api_base = settings.AZURE_OPENAI_DEPLOYMENT_ENDPOINT

            return AzureChatOpenAI(
                openai_api_type="azure",
                model=model_id,
                temperature=temperature,
                openai_api_version=openai_api_version,
                openai_api_key=openai_api_key,
                deployment_name=deployment_name,
                azure_endpoint=openai_api_base,
                max_tokens=answer_token,
                streaming=streaming,
            )

        except Exception as exc:
            logger.warning(f"error in initializing {model_id} \n {str(exc)}")

    @classmethod
    def create_from_model_config(
        cls,
        deployment_name: str,
        model_id: str,
        max_token_limit: int,
        answer_token: int,
        memory_token: Optional[int] = 0,
    ) -> "AzureModelConfig":
        """
        Create an instance of AzureModelConfig with the provided configuration parameters.

        Args:
            memory_token:
            deployment_name (str): The name of the deployment.
            model_id (str): The name of the model.
            max_token_limit (int): The maximum token limit for the model.
            answer_token (int): The token representing an answer.

        Returns:
            AzureModelConfig: An instance of AzureModelConfig with the specified configuration.
        """
        return cls(
            deployment_name=deployment_name,
            model_id=model_id,
            max_token_limit=max_token_limit,
            answer_token=answer_token,
            memory_token=memory_token,
        )


gpt_35_turbo = AzureModelConfig.create_from_model_config(
    deployment_name=settings.AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME,
    model_id="gpt-3.5-turbo",
    max_token_limit=4000,
    answer_token=300,
    memory_token=0,
)

gpt_35_turbo_16k = AzureModelConfig.create_from_model_config(
    deployment_name=settings.AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME,
    model_id="gpt-3.5-turbo-16k",
    max_token_limit=16000,
    answer_token=400,
    memory_token=0,
)
