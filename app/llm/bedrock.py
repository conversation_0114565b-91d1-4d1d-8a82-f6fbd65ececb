"""
AWS Bedrock utility functions for AI-powered data extraction.

This module provides production-ready Bedrock functionality for:
- Nova Pro model integration
- Structured data extraction from text
- Custom prompt handling
- Comprehensive logging for AWS environments
"""

import boto3
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
from app.core.configuration import settings

# Configure logging
logger = logging.getLogger(__name__)


class BedrockProcessor:
    """
    AWS Bedrock processor for AI-powered data extraction using Nova Pro.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Bedrock processor.
        
        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing BedrockProcessor...")
        
        try:
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
            
            self.region = aws_region
            
            logger.info("✅ Successfully initialized BedrockProcessor")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Timestamp: {datetime.now().isoformat()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize BedrockProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    def get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for document extraction.
        
        Returns:
            str: Default system prompt
        """
        return """You are an expert document data extraction AI assistant. Your task is to analyze document text and extract key business information in a structured JSON format.

You should extract the following information when available:
- Vendor/Company Name
- Invoice Number/Document ID  
- Date (Invoice Date, Document Date)
- Total Amount
- Currency
- Customer Information
- Line Items (if applicable)
- Any other relevant business data

Always return valid JSON format with clear field names. If a field is not found, use null as the value.
Be precise and accurate in your extraction."""
    
    def get_default_user_prompt(self, text_content: str) -> str:
        """
        Get the default user prompt for document extraction.
        
        Args:
            text_content: Document text to analyze
            
        Returns:
            str: Formatted user prompt
        """
        return f"""Please analyze the following document text and extract key business information in JSON format:

Document Text:
{text_content}

Extract the information and return as a valid JSON object."""
    
    async def extract_data_with_nova_pro(self, text_content: str, 
                                       system_prompt: Optional[str] = None,
                                       user_prompt: Optional[str] = None,
                                       temperature: float = 0.1,
                                       max_tokens: int = 4000,
                                       top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model.
        
        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature (default: 0.1)
            max_tokens: Maximum tokens (default: 4000)
            top_p: Top-p sampling (default: 0.9)
            
        Returns:
            dict: Extracted structured data
        """
        logger.info("🧠 Starting data extraction with Bedrock Nova Pro...")
        logger.info(f"   Text length: {len(text_content)} characters")
        logger.info(f"   Temperature: {temperature}")
        logger.info(f"   Max tokens: {max_tokens}")
        logger.info(f"   Top-p: {top_p}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")
        
        try:
            # Use provided prompts or defaults
            if not system_prompt:
                system_prompt = self.get_default_system_prompt()

            if not user_prompt:
                user_prompt = self.get_default_user_prompt(text_content)

            # Combine system and user prompts since Nova Pro doesn't support system role
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Prepare the request for Nova Pro (using correct format with content array)
            request_body = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": combined_prompt
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "maxTokens": max_tokens,
                    "temperature": temperature
                }
            }
            
            logger.info("📤 Sending request to Bedrock Nova Pro...")
            logger.debug(f"   Model: amazon.nova-pro-v1:0")
            logger.debug(f"   Request body keys: {list(request_body.keys())}")
            
            # Call Bedrock Nova Pro
            response = self.bedrock_client.invoke_model(
                modelId="amazon.nova-pro-v1:0",
                contentType="application/json",
                accept="application/json",
                body=json.dumps(request_body)
            )
            
            # Parse response
            response_body = json.loads(response['body'].read())
            
            logger.info("✅ Bedrock Nova Pro extraction completed successfully!")
            logger.info(f"   Response received at: {datetime.now().isoformat()}")
            
            # Extract the content from Nova Pro response
            if 'output' in response_body and 'message' in response_body['output']:
                extracted_content = response_body['output']['message']['content'][0]['text']
                logger.info(f"   Extracted content length: {len(extracted_content)} characters")
                
                # Try to parse as JSON
                try:
                    extracted_data = json.loads(extracted_content)
                    logger.info("✅ Successfully parsed extracted data as JSON")
                    logger.info(f"   Extracted fields: {list(extracted_data.keys()) if isinstance(extracted_data, dict) else 'Non-dict response'}")
                    return extracted_data
                except json.JSONDecodeError:
                    logger.warning("⚠️  Extracted content is not valid JSON, returning as text")
                    return {"extracted_text": extracted_content, "raw_response": response_body}
            else:
                logger.warning("⚠️  Unexpected response format from Nova Pro")
                return {"raw_response": response_body}
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ Bedrock ClientError: {error_code} - {error_message}")
            logger.error(f"   Model: amazon.nova-pro-v1:0")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
            
        except Exception as e:
            logger.error(f"❌ Unexpected error during Bedrock extraction: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    async def process_text_extraction(self, text_content: str,
                                    system_prompt: Optional[str] = None,
                                    user_prompt: Optional[str] = None,
                                    temperature: float = 0.1,
                                    max_tokens: int = 4000,
                                    top_p: float = 0.9) -> Dict[str, Any]:
        """
        Complete Bedrock processing: extract structured data from text.
        
        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling
            
        Returns:
            dict: Complete Bedrock processing results
        """
        logger.info("🎯 Starting complete Bedrock processing...")
        logger.info(f"   Text length: {len(text_content)} characters")
        
        start_time = datetime.now()
        
        try:
            # Extract structured data
            structured_data = await self.extract_data_with_nova_pro(
                text_content, system_prompt, user_prompt, temperature, max_tokens, top_p
            )
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                "bedrock_metadata": {
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "input_text_length": len(text_content),
                    "model_id": "amazon.nova-pro-v1:0",
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": top_p,
                    "aws_region": self.region
                },
                "structured_data": structured_data
            }
            
            logger.info("✅ Bedrock processing completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            logger.error("❌ Bedrock processing failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            raise


# Utility functions for easy access
async def extract_data_with_bedrock(text_content: str,
                                  system_prompt: Optional[str] = None,
                                  user_prompt: Optional[str] = None,
                                  temperature: float = 0.1,
                                  max_tokens: int = 4000,
                                  top_p: float = 0.9,
                                  aws_region: str = None,
                                  aws_access_key_id: str = None,
                                  aws_secret_access_key: str = None) -> Dict[str, Any]:
    """
    Extract structured data using Bedrock Nova Pro.
    
    Args:
        text_content: Text content to analyze
        system_prompt: Optional custom system prompt
        user_prompt: Optional custom user prompt
        temperature: Model temperature
        max_tokens: Maximum tokens
        top_p: Top-p sampling
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)
        
    Returns:
        dict: Bedrock processing results
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY
    
    processor = BedrockProcessor(region, access_key, secret_key)
    return await processor.process_text_extraction(
        text_content, system_prompt, user_prompt, temperature, max_tokens, top_p
    )
