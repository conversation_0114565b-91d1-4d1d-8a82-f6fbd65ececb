"""
S3 Service for handling file operations with AWS S3.
"""
import os
import tempfile
from typing import List, Optional, BinaryIO, Dict, Any
from pathlib import Path

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from fastapi import UploadFile, HTTPException, status

from app.core.configuration import settings
from loguru import logger


class S3Service:
    """
    Service class for handling S3 operations with proper error handling and logging.
    """
    
    def __init__(self):
        """Initialize S3 client with configuration."""
        try:
            # Pass None instead of empty strings to allow boto3 to use its credential provider chain
            aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
            aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
            # aws_session_token = settings.AWS_SESSION_TOKEN or None

            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key,
                # aws_session_token=aws_session_token,
                region_name=settings.AWS_REGION
            )
            logger.info("S3 client initialized successfully")
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AWS credentials not configured"
            )
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to initialize S3 service"
            )

    def create_bucket(self, bucket_name: str) -> bool:
        """
        Create an S3 bucket if it doesn't exist.
        
        Args:
            bucket_name: Name of the bucket to create
            
        Returns:
            bool: True if bucket created or exists, False otherwise
        """
        try:
            # Check if bucket exists
            self.s3_client.head_bucket(Bucket=bucket_name)
            logger.info(f"Bucket {bucket_name} already exists")
            return True
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                # Bucket doesn't exist, create it
                try:
                    self.s3_client.create_bucket(
                        Bucket=bucket_name,
                        CreateBucketConfiguration={
                            'LocationConstraint': settings.AWS_REGION
                        } if settings.AWS_REGION != 'us-east-1' else {}
                    )
                    logger.info(f"Created bucket {bucket_name}")
                    return True
                except ClientError as create_error:
                    logger.error(f"Failed to create bucket {bucket_name}: {create_error}")
                    return False
            else:
                logger.error(f"Error checking bucket {bucket_name}: {e}")
                return False

    def upload_file(
        self, 
        file: UploadFile, 
        bucket_name: str, 
        folder_prefix: str = "",
        custom_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upload a file to S3 bucket.
        
        Args:
            file: FastAPI UploadFile object
            bucket_name: Target S3 bucket name
            folder_prefix: Optional folder prefix within the bucket
            custom_filename: Optional custom filename (uses original if not provided)
            
        Returns:
            Dict containing upload details
        """
        try:
            # Ensure bucket exists
            if not self.create_bucket(bucket_name):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to create or access bucket {bucket_name}"
                )

            # Determine object key
            filename = custom_filename or file.filename
            if not filename:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No filename provided"
                )

            object_key = os.path.join(folder_prefix, filename).replace("\\", "/")
            
            # Upload file
            self.s3_client.upload_fileobj(
                file.file,
                bucket_name,
                object_key,
                ExtraArgs={
                    'ContentType': file.content_type or 'application/octet-stream'
                }
            )
            
            # Generate presigned URL for immediate access (optional)
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': object_key},
                ExpiresIn=3600  # 1 hour
            )
            
            result = {
                "bucket_name": bucket_name,
                "object_key": object_key,
                "filename": filename,
                "file_size": file.size,
                "content_type": file.content_type,
                "s3_url": f"s3://{bucket_name}/{object_key}",
                "presigned_url": presigned_url
            }
            
            logger.info(f"Successfully uploaded {filename} to {bucket_name}/{object_key}")
            return result
            
        except ClientError as e:
            logger.error(f"S3 upload error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during upload: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error during file upload"
            )

    def download_file(self, bucket_name: str, object_key: str) -> tempfile.NamedTemporaryFile:
        """
        Download a file from S3 to a temporary file.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            NamedTemporaryFile object containing the downloaded file
        """
        try:
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            self.s3_client.download_fileobj(bucket_name, object_key, temp_file)
            temp_file.seek(0)
            logger.info(f"Successfully downloaded {object_key} from {bucket_name}")
            return temp_file
        except ClientError as e:
            logger.error(f"S3 download error: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {object_key}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during download: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error during file download"
            )

    def list_files(
        self, 
        bucket_name: str, 
        folder_prefix: str = "",
        max_keys: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        List files in an S3 bucket with optional folder prefix.
        
        Args:
            bucket_name: S3 bucket name
            folder_prefix: Optional folder prefix to filter results
            max_keys: Maximum number of keys to return
            
        Returns:
            List of file information dictionaries
        """
        try:
            files = []
            paginator = self.s3_client.get_paginator('list_objects_v2')
            
            pagination_kwargs = {
                'Bucket': bucket_name,
                'PaginationConfig': {'MaxItems': max_keys}
            }
            if folder_prefix:
                pagination_kwargs['Prefix'] = folder_prefix
            
            page_iterator = paginator.paginate(**pagination_kwargs)
            
            for page in page_iterator:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        files.append({
                            'key': obj['Key'],
                            'size': obj['Size'],
                            'last_modified': obj['LastModified'].isoformat(),
                            'etag': obj['ETag'].strip('"'),
                            'storage_class': obj.get('StorageClass', 'STANDARD')
                        })
            
            logger.info(f"Found {len(files)} files in {bucket_name}/{folder_prefix}")
            return files
            
        except ClientError as e:
            logger.error(f"S3 list error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list files: {str(e)}"
            )

    def delete_file(self, bucket_name: str, object_key: str) -> bool:
        """
        Delete a file from S3 bucket.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key to delete
            
        Returns:
            bool: True if deletion successful
        """
        try:
            self.s3_client.delete_object(Bucket=bucket_name, Key=object_key)
            logger.info(f"Successfully deleted {object_key} from {bucket_name}")
            return True
        except ClientError as e:
            logger.error(f"S3 delete error: {e}")
            return False

    def get_file_url(self, bucket_name: str, object_key: str, expires_in: int = 3600) -> str:
        """
        Generate a presigned URL for file access.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            expires_in: URL expiration time in seconds
            
        Returns:
            str: Presigned URL
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': object_key},
                ExpiresIn=expires_in
            )
            return url
        except ClientError as e:
            logger.error(f"Failed to generate presigned URL: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate file access URL"
            )

    def check_file_exists(self, bucket_name: str, object_key: str) -> bool:
        """
        Check if a file exists in S3 bucket.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            bool: True if file exists
        """
        try:
            self.s3_client.head_object(Bucket=bucket_name, Key=object_key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                logger.error(f"Error checking file existence: {e}")
                return False


# Global S3 service instance
s3_service = S3Service() 