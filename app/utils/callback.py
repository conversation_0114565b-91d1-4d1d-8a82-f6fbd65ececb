from datetime import datetime, timezone
from typing import Any, Coroutine, Dict, List
from uuid import UUID

from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
from langchain_core.messages.base import BaseMessage
from langchain_core.outputs import LLMResult


class LLMResponseHandler(AsyncIteratorCallbackHandler):
    def __init__(self):
        # values will be added later by callback functions
        self.start_time = None
        self.end_time = None
        self.error = None
        super().__init__()

    async def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any
    ) -> Coroutine[Any, Any, Any]:
        # otherwise giving not implemented error
        self.start_time = datetime.now(timezone.utc).replace(tzinfo=None)

        return await super().on_chat_model_start(
            serialized,
            messages,
            run_id=run_id,
            parent_run_id=parent_run_id,
            tags=tags,
            metadata=metadata,
        )

    async def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ):
        return await super().on_llm_start(serialized, prompts, **kwargs)

    async def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        self.end_time = datetime.now(timezone.utc).replace(tzinfo=None)
        return await super().on_llm_end(response, **kwargs)

    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> None:
        #: log the error here
        self.error = str(error)
        await super().on_llm_error(error, **kwargs)
