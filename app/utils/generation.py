import asyncio
from typing import List, Optional

from langchain_core.documents import Document
from langchain_core.runnables import RunnableSequence
from langchain_core.messages import AIMessageChunk

from loguru import logger
from app.utils.callback import LLMResponseHandler


class AnsGenerator:
    def __init__(self, llm, prompt, memory: Optional = None) -> None:
        self.llm = llm
        self.prompt = prompt
        self.memory = memory
        self.chain = prompt | llm

    def get_chain_and_kwargs(
        self,
        question: str,
        relevant_documents: Optional[List[Document]] = None,
        **kwargs,
    ):
        if relevant_documents:
            chain_kwargs = {
                "summaries": relevant_documents,
                "question": question,
                **kwargs,
            }
        else:
            chain_kwargs = {"question": question, **kwargs}

        return self.chain, chain_kwargs

    @staticmethod
    def generate_response(
        chain: RunnableSequence, chain_kwargs, callback: Optional[LLMResponseHandler] = None
    ):
        callbacks = [callback] if callback else None
        return chain.invoke(input=chain_kwargs, config={"callbacks": callbacks})
    
    @staticmethod
    async def agenerate_response(
        chain: RunnableSequence,
        chain_kwargs,
        callback: Optional[LLMResponseHandler] = None,
    ):
        """
        Asynchronously generate response from the chain
        """
        try:
            
            config = {"callbacks": [callback]} if callback else {}
            
            async for chunk in chain.astream(input=chain_kwargs, config=config):
                
                if isinstance(chunk, AIMessageChunk):
                    content = chunk.content
                    yield content
                else:
                    logger.warning(f"Unexpected chunk type: {type(chunk)}, content: {chunk}")
                
        except Exception as exc:
            logger.exception(f"Error in streaming response: {exc}")
            raise exc
        finally:
                callback.done.set()
