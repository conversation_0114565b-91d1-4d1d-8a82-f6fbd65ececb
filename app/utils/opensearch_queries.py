from typing import Dict, Any


def get_contextual_messages_query(
    user_id: str, chat_id: str, size: int = 3
) -> Dict[str, Any]:
    return {
        "_source": ["question", "answer"],
        "query": {
            "bool": {
                "filter": [
                    {"term": {"user_id": user_id}},
                    {"term": {"chat_id": chat_id}},
                ]
            }
        },
        "sort": [{"answer_at": {"order": "desc"}}],
        "size": size,
    }
