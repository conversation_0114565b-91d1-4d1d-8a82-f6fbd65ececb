"""
AWS Textract utility functions for document analysis and OCR.

This module provides production-ready Textract functionality for:
- Document analysis with OCR
- Text extraction from various document types
- Async operations for scalability
- Comprehensive logging for AWS environments
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

import boto3
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
from app.core.configuration import settings

# Configure logging
logger = logging.getLogger(__name__)


class TextractProcessor:
    """
    AWS Textract processor for document analysis and text extraction.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Textract processor.

        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing TextractProcessor...")

        try:
            self.textract_client = boto3.client(
                'textract',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            self.s3_client = boto3.client(
                's3',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            self.region = aws_region

            logger.info("✅ Successfully initialized TextractProcessor")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Timestamp: {datetime.now().isoformat()}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize TextractProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise

    def _get_file_metadata(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Get file metadata from S3.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: File metadata including size and content type
        """
        try:
            response = self.s3_client.head_object(Bucket=bucket_name, Key=object_key)
            return {
                'size': response['ContentLength'],
                'content_type': response.get('ContentType', ''),
                'last_modified': response['LastModified']
            }
        except ClientError as e:
            logger.error(f"❌ Failed to get file metadata: {str(e)}")
            raise

    def _should_use_sync_method(self, bucket_name: str, object_key: str) -> bool:
        """
        Determine if sync method should be used based on file criteria.

        Criteria for sync method:
        - File type is PDF
        - File size is < 5 MB
        - Single page (estimated based on size)

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            bool: True if sync method should be used
        """
        try:
            # Get file metadata
            metadata = self._get_file_metadata(bucket_name, object_key)
            file_size = metadata['size']

            # Check if it's a PDF file
            is_pdf = (object_key.lower().endswith('.pdf') or
                     metadata.get('content_type', '').lower() == 'application/pdf')

            # Size criteria: < 5 MB (5 * 1024 * 1024 bytes)
            size_limit = 5 * 1024 * 1024
            is_small_file = file_size < size_limit

            # For PDFs, estimate if it's likely a single page
            # Rough estimation: single page PDF is typically < 1 MB
            likely_single_page = file_size < (1 * 1024 * 1024) if is_pdf else True

            should_use_sync = is_pdf and is_small_file and likely_single_page

            logger.info(f"📊 File analysis for sync/async decision:")
            logger.info(f"   File: s3://{bucket_name}/{object_key}")
            logger.info(f"   Size: {file_size:,} bytes ({file_size / (1024*1024):.2f} MB)")
            logger.info(f"   Is PDF: {is_pdf}")
            logger.info(f"   Under 5MB: {is_small_file}")
            logger.info(f"   Likely single page: {likely_single_page}")
            logger.info(f"   Decision: {'SYNC' if should_use_sync else 'ASYNC'}")

            return should_use_sync

        except Exception as e:
            logger.warning(f"⚠️  Failed to analyze file for sync/async decision: {str(e)}")
            logger.warning("   Defaulting to ASYNC method")
            return False

    def analyze_document_sync(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using synchronous Textract detect_document_text.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting SYNC Textract document analysis...")
        logger.info(f"   S3 Location: s3://{bucket_name}/{object_key}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")

        try:
            # Use detect_document_text for sync processing
            response = self.textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )

            logger.info(f"✅ SYNC Textract analysis completed successfully!")
            logger.info(f"   Blocks detected: {len(response.get('Blocks', []))}")

            return response

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ SYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during SYNC Textract analysis: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise

    async def analyze_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting Textract document analysis...")
        logger.info(f"   S3 Location: s3://{bucket_name}/{object_key}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")
        
        try:
            # Start document analysis
            response = self.textract_client.start_document_analysis(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                },
                FeatureTypes=['TABLES', 'FORMS', 'LAYOUT']
            )
            
            job_id = response['JobId']
            logger.info(f"✅ Textract analysis started successfully")
            logger.info(f"   Job ID: {job_id}")
            
            # Poll for completion
            logger.info("⏳ Waiting for Textract analysis to complete...")
            poll_count = 0
            start_time = datetime.now()
            
            while True:
                poll_count += 1
                elapsed_time = (datetime.now() - start_time).total_seconds()
                
                result = self.textract_client.get_document_analysis(JobId=job_id)
                status = result['JobStatus']
                
                logger.info(f"   Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time:.1f}s)")
                
                if status == 'SUCCEEDED':
                    logger.info(f"✅ Textract analysis completed successfully!")
                    logger.info(f"   Total processing time: {elapsed_time:.1f} seconds")
                    logger.info(f"   Total polls: {poll_count}")
                    logger.info(f"   Pages analyzed: {len(result.get('Blocks', []))}")
                    return result
                    
                elif status == 'FAILED':
                    error_msg = f"Textract analysis failed after {elapsed_time:.1f} seconds"
                    logger.error(f"❌ {error_msg}")
                    logger.error(f"   Job ID: {job_id}")
                    raise Exception(error_msg)
                    
                elif status in ['IN_PROGRESS']:
                    logger.debug(f"   ⏳ Analysis still in progress... waiting 5 seconds")
                    await asyncio.sleep(5)
                else:
                    logger.warning(f"   ⚠️  Unknown status: {status}")
                    await asyncio.sleep(5)
                    
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ Textract ClientError: {error_code} - {error_message}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
            
        except Exception as e:
            logger.error(f"❌ Unexpected error during Textract analysis: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    def extract_text_from_result(self, textract_result: Dict[str, Any]) -> str:
        """
        Extract clean text from Textract analysis result.
        
        Args:
            textract_result: Result from Textract analysis
            
        Returns:
            str: Extracted text content
        """
        logger.info("📝 Extracting text from Textract results...")
        
        try:
            blocks = textract_result.get('Blocks', [])
            text_blocks = [block for block in blocks if block['BlockType'] == 'LINE']
            
            extracted_text = '\n'.join([block.get('Text', '') for block in text_blocks])
            
            logger.info(f"✅ Text extraction completed")
            logger.info(f"   Total blocks processed: {len(blocks)}")
            logger.info(f"   Text lines extracted: {len(text_blocks)}")
            logger.info(f"   Total characters: {len(extracted_text)}")
            
            return extracted_text
            
        except Exception as e:
            logger.error(f"❌ Error extracting text from Textract result: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    async def process_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Complete Textract processing: tries sync first, then async if needed.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: Complete Textract processing results
        """
        logger.info("🎯 Starting intelligent Textract processing...")
        logger.info(f"   S3 Location: s3://{bucket_name}/{object_key}")

        start_time = datetime.now()
        processing_method = "UNKNOWN"

        try:
            # Determine processing method based on file criteria
            should_use_sync = self._should_use_sync_method(bucket_name, object_key)

            if should_use_sync:
                logger.info("🚀 Attempting SYNC processing...")
                processing_method = "SYNC"
                try:
                    # Try sync method first
                    textract_result = self.analyze_document_sync(bucket_name, object_key)
                    logger.info("✅ SYNC processing successful!")

                except Exception as sync_error:
                    logger.warning(f"⚠️  SYNC processing failed: {str(sync_error)}")
                    logger.info("🔄 Falling back to ASYNC processing...")
                    processing_method = "ASYNC_FALLBACK"
                    textract_result = await self.analyze_document(bucket_name, object_key)
                    logger.info("✅ ASYNC fallback processing successful!")
            else:
                logger.info("🚀 Using ASYNC processing directly...")
                processing_method = "ASYNC"
                textract_result = await self.analyze_document(bucket_name, object_key)
                logger.info("✅ ASYNC processing successful!")

            # Extract text
            extracted_text = self.extract_text_from_result(textract_result)

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "textract_metadata": {
                    "s3_location": f"s3://{bucket_name}/{object_key}",
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "processing_method": processing_method,
                    "textract_blocks_count": len(textract_result.get('Blocks', [])),
                    "extracted_text_length": len(extracted_text),
                    "aws_region": self.region
                },
                "textract_raw_result": textract_result,
                "extracted_text": extracted_text
            }

            logger.info("✅ Textract processing completed successfully!")
            logger.info(f"   Processing method: {processing_method}")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Textract processing failed!")
            logger.error(f"   Processing method attempted: {processing_method}")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            raise


# Utility functions for easy access
async def analyze_document_with_textract(bucket_name: str, object_key: str,
                                       aws_region: Optional[str] = None,
                                       aws_access_key_id: Optional[str] = None,
                                       aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Analyze document using Textract.
    
    Args:
        bucket_name: S3 bucket name
        object_key: S3 object key
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)
        
    Returns:
        dict: Textract processing results
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY
    
    processor = TextractProcessor(region, access_key, secret_key)
    return await processor.process_document(bucket_name, object_key)


async def process_document_with_textract(bucket_name: str, object_key: str,
                                       aws_region: Optional[str] = None,
                                       aws_access_key_id: Optional[str] = None,
                                       aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Process document using intelligent Textract (sync first, then async fallback).

    This function automatically determines the best processing method:
    - For PDFs < 5MB and likely single page: tries sync first, falls back to async
    - For other files or larger PDFs: uses async directly

    Args:
        bucket_name: S3 bucket name
        object_key: S3 object key
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Textract processing results with processing method metadata
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

    processor = TextractProcessor(region, access_key, secret_key)
    return await processor.process_document(bucket_name, object_key)


# Sample usage
async def main():
    result = await process_document_with_textract(
        'document-extraction-logistically', 
        r'/home/<USER>/Documents/repositories/document-data-extraction/data/COUNET11522098_INV.pdf'
    )
    print(result)


if __name__ == "__main__":
    asyncio.run(main())