from typing import Union, Optional

from langchain_openai import ChatOpenAI
from langchain_aws import ChatBedrock
from langchain_core.runnables import RunnableWithMessageHistory
from langchain_elasticsearch.chat_history import ElasticsearchChatMessageHistory
from langchain_redis.chat_message_history import RedisChatMessageHistory
from opensearchpy import OpenSearch

from app.core.configuration import settings
from app.core.opensearch import os_client


class RedisChatManager:
    def __init__(self, redis_url: str, redis_ttl: int):
        self.redis_url = redis_url
        self.redis_ttl = redis_ttl

    def _get_conversation_history(
        self, session_id: str, key_prefix: str
    ) -> RedisChatMessageHistory:
        return RedisChatMessageHistory(
            session_id=session_id,
            key_prefix=key_prefix,
            url=self.redis_url,
            ttl=self.redis_ttl,
        )

    def delete_conversation_history(self, session_id: str, key_prefix: str) -> None:
        history = self._get_conversation_history(
            session_id=session_id, key_prefix=key_prefix
        )
        history.clear()

    def get_conversation_with_history(
        self,
        chain,
        session_id: str,
        key_prefix: str,
    ) -> RunnableWithMessageHistory:
        message_history = self._get_conversation_history(
            session_id=session_id, key_prefix=key_prefix
        )
        
        return RunnableWithMessageHistory(
            chain,
            message_history,
            input_messages_key="text",
            history_messages_key="chat_history",
        )


redis_chat_manager = RedisChatManager(
    redis_url=settings.REDIS_URL, redis_ttl=settings.REDIS_TTL
)


class OpenSearchChatManager:
    def __init__(self, os_client: OpenSearch, index_name: str) -> None:
        self.os_client = os_client
        self.index_name = index_name

    def _get_conversation_history(self, session_id: str) -> ElasticsearchChatMessageHistory:
        return ElasticsearchChatMessageHistory(
            index=self.index_name,
            session_id=session_id,
            es_connection=self.os_client,
        )

    def get_conversation_with_history(
        self,
        chain,
        session_id: str,
    ) -> RunnableWithMessageHistory:
        message_history = self._get_conversation_history(session_id)
        
        return RunnableWithMessageHistory(
            chain,
            message_history,
            input_messages_key="text",
            history_messages_key="chat_history",
        )


opensearch_chat_manager = OpenSearchChatManager(
    os_client, settings.OPENSEARCH_VECTOR_INDEX
)
