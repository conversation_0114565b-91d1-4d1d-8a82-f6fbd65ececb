"""
Schemas for S3 operations and file management.
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class S3UploadRequest(BaseModel):
    """Schema for S3 upload request."""
    bucket_name: str = Field(..., description="Target S3 bucket name")
    folder_prefix: str = Field(default="", description="Optional folder prefix within bucket")
    custom_filename: Optional[str] = Field(None, description="Optional custom filename")

    @validator('bucket_name')
    def validate_bucket_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Bucket name cannot be empty')
        if len(v) < 3 or len(v) > 63:
            raise ValueError('Bucket name must be between 3 and 63 characters')
        if not v[0].isalnum():
            raise ValueError('Bucket name must start with a letter or number')
        if not v.replace('-', '').replace('.', '').isalnum():
            raise ValueError('Bucket name can only contain letters, numbers, hyphens, and dots')
        return v.lower()


class S3UploadResponse(BaseModel):
    """Schema for S3 upload response."""
    success: bool = Field(..., description="Upload success status")
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="S3 object key")
    filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    content_type: Optional[str] = Field(None, description="File content type")
    s3_url: str = Field(..., description="S3 URI")
    presigned_url: str = Field(..., description="Presigned URL for immediate access")
    uploaded_at: datetime = Field(default_factory=datetime.utcnow, description="Upload timestamp")


class S3FileInfo(BaseModel):
    """Schema for S3 file information."""
    key: str = Field(..., description="S3 object key")
    size: int = Field(..., description="File size in bytes")
    last_modified: str = Field(..., description="Last modified timestamp")
    etag: str = Field(..., description="File ETag")
    storage_class: str = Field(..., description="S3 storage class")


class S3ListResponse(BaseModel):
    """Schema for S3 list files response."""
    bucket_name: str = Field(..., description="S3 bucket name")
    folder_prefix: str = Field(..., description="Folder prefix used for listing")
    files: List[S3FileInfo] = Field(..., description="List of files")
    total_count: int = Field(..., description="Total number of files")
    max_keys: int = Field(..., description="Maximum keys requested")


class S3DownloadRequest(BaseModel):
    """Schema for S3 download request."""
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="S3 object key to download")


class S3DeleteRequest(BaseModel):
    """Schema for S3 delete request."""
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="S3 object key to delete")


class S3DeleteResponse(BaseModel):
    """Schema for S3 delete response."""
    success: bool = Field(..., description="Delete success status")
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="Deleted object key")
    deleted_at: datetime = Field(default_factory=datetime.utcnow, description="Delete timestamp")


class S3PresignedUrlRequest(BaseModel):
    """Schema for presigned URL request."""
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="S3 object key")
    expires_in: int = Field(default=3600, ge=1, le=604800, description="URL expiration time in seconds (1 hour to 7 days)")


class S3PresignedUrlResponse(BaseModel):
    """Schema for presigned URL response."""
    bucket_name: str = Field(..., description="S3 bucket name")
    object_key: str = Field(..., description="S3 object key")
    presigned_url: str = Field(..., description="Presigned URL")
    expires_in: int = Field(..., description="URL expiration time in seconds")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="URL generation timestamp")


class S3BucketInfo(BaseModel):
    """Schema for S3 bucket information."""
    name: str = Field(..., description="Bucket name")
    creation_date: Optional[str] = Field(None, description="Bucket creation date")
    region: str = Field(..., description="Bucket region")
    exists: bool = Field(..., description="Whether bucket exists")


class S3HealthCheck(BaseModel):
    """Schema for S3 health check response."""
    service_status: str = Field(..., description="S3 service status")
    credentials_configured: bool = Field(..., description="Whether AWS credentials are configured")
    default_bucket_accessible: Optional[bool] = Field(None, description="Whether default bucket is accessible")
    region: str = Field(..., description="AWS region")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp") 