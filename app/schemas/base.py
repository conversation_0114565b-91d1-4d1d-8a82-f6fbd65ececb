from datetime import datetime
from typing import Any, Dict

from pydantic import BaseModel


# base time stamp schema inherited by all the response schema
class TimeStampSchema(BaseModel):
    created_at: datetime
    updated_at: datetime


class AccessTokenSchema(BaseModel):
    access_token: str


class RefreshTokenSchema(BaseModel):
    refresh_token: str


class BaseResponseSchema(BaseModel):
    ok: bool
    message: str
    data: Dict[str, Any]


class BasePaginatedResponseSchema(BaseResponseSchema):
    page: int
    page_size: int
    total: int


class BaseTimestampResponseSchema(BaseResponseSchema, TimeStampSchema):
    pass


class TokenResponseSchema(AccessTokenSchema, RefreshTokenSchema):
    token_type: str


class BaseExceptionResponse(BaseModel):
    ok: bool = False
    error_code: str
    detail: str
