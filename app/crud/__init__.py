from abc import ABC, abstractmethod
from typing import Optional, TypeVar, Any

from pydantic import BaseModel

ModelConfigType = TypeVar("ModelConfigType")


class ModelConfigBase(BaseModel, ABC):
    """
    Base class for defining model configurations.

    Attributes:
        name (str): The name of the model.
        info (str): Information about the model, its capabilities, and intended use.
        model (Any): The model object or reference (you can specify a more specific type here if needed).
        max_token_limit (int): The maximum number of tokens that can be processed by the model in a single request.
        buffer_token (Optional[int]): The optional buffer token count (default is 0).
        answer_token (Optional[int]): The optional recommended token count for generating answers or responses.

    Abstract Methods:
        _initialize_model(self, *args, **kwargs): Abstract method to initialize the model. Subclasses must implement this method.
        create_from_model_config(cls, *args, **kwargs) -> ModelConfigType: Abstract class method to get model configuration. Subclasses must implement this method.

    Example Usage:
        Define a subclass of ModelConfigBase and provide concrete implementations for the abstract methods.
    """

    name: str
    info: str
    model: Any  # You can specify a more specific type here if needed
    max_token_limit: int
    answer_token: Optional[int]
    buffer_token: Optional[int] = 0
    memory_token: Optional[int] = 0

    @abstractmethod
    def _initialize_model(self, *args, **kwargs):
        raise NotImplementedError(
            "Subclasses must implement the '_initialize_model' method"
        )

    @classmethod
    @abstractmethod
    def get_model_config(cls, *args, **kwargs) -> ModelConfigType:
        raise NotImplementedError(
            "Subclasses must implement the '_initialize_model' method"
        )
