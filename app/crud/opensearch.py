from typing import Any, Dict, List, Optional

from opensearchpy import OpenSearch

from app.core.opensearch import os_client


class OpenSearchManager:
    def __init__(
        self,
        index_name: str,
        os_client: OpenSearch,
        **kwargs,
    ) -> None:
        self.index_name = index_name
        self.os_client = os_client

    def insert(self, body: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        return self.os_client.index(index=self.index_name, body=body, **kwargs)

    def update(self, doc_id, body, **kwargs) -> Dict[str, Any]:
        return self.os_client.update(
            index=self.index_name, id=doc_id, body=body, **kwargs
        )

    def delete(self, doc_id, **kwargs):
        """
        The `delete` function deletes a document from an opensearch index using the provided ID.

        :param doc_id: The `id` parameter is the unique identifier of the document that you want to delete from
        the opensearch index
        :return: The delete method is returning the result of the delete operation performed by the os_client.
        """
        return self.os_client.delete(index=self.index_name, id=doc_id, **kwargs)

    def get(self, doc_id, **kwargs) -> Dict[str, Any]:
        """
        The function `get` retrieves a document from an opensearch index based on its ID.

        :param doc_id: The `id` parameter is the unique identifier of the document you want to retrieve from the
        opensearch index
        :return: The `get` method is returning the result of the `self.os_client.get` method.
        """
        return self.os_client.get(index=self.index_name, id=doc_id, **kwargs)

    def search_with_pagination(
        self, search_query: Dict[str, Any], start_index: int, page_size: int
    ) -> List[Optional[Dict[str, Any]]]:
        # add start index and page size to search query
        search_query["from"] = start_index
        search_query["size"] = page_size

        response = self.os_client.search(index=self.index_name, body=search_query)
        hits = response["hits"]["hits"]

        documents = []

        for hit in hits:
            data = hit["_source"]  # will contain all doc data
            data["id"] = hit["_id"]
            documents.append(data)

        return documents

    @classmethod
    def create_from_index_name(cls, index_name: str, **kwargs) -> "OpenSearchManager":
        return cls(index_name=index_name, **kwargs)


opensearch_manager = OpenSearchManager(
    index_name="YOUR_INDEX_NAME", os_client=os_client
)
