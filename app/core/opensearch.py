from typing import List

from opensearchpy import OpenSearch

from app.core.configuration import settings


def _setup_opensearch_client(
    hosts: List[str],
    username: str,
    password: str,
    **kwargs,
) -> OpenSearch:
    """
    The function `_setup_opensearch_client` sets up a connection to an OpenSearch instance using the
    provided hosts, username, password, and additional keyword arguments.

    :param hosts: A list of strings representing the hostnames or IP addresses of the OpenSearch
    instances you want to connect to
    :type hosts: List[str]
    :param username: The `username` parameter is a string that represents the username used for
    authentication when connecting to the OpenSearch instance
    :type username: str
    :param password: The `password` parameter is a string that represents the password used to
    authenticate the connection to the OpenSearch instance
    :type password: str
    :return: an instance of the OpenSearch class.
    """
    # Connect to your OpenSearch instance
    return OpenSearch(hosts=hosts, http_auth=(username, password), **kwargs)


os_client = _setup_opensearch_client(
    hosts=[settings.OPENSEARCH_HOST],
    username=settings.OPENSEARCH_USERNAME,
    password=settings.OPENSEARCH_PASSWORD,
)
