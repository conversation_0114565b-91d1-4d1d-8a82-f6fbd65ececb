"""
Module for managing FastAPI application settings and configuration.
"""
import os
from typing import List
from pathlib import Path
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
parent_dir = Path(__file__).resolve().parent.parent.parent
dotenv_path = parent_dir / ".env"
load_dotenv(dotenv_path)

__all__ = ["settings"]


class Settings(BaseSettings):
    """
    Represents a configuration class for loading environment variables.
    This class loads environment variables.
    """

    # project configuration
    PROJECT_NAME: str = os.environ.get("PROJECT_NAME", "document-data-extraction")
    HOST: str = os.environ.get("HOST", "0.0.0.0")
    PORT: int = os.environ.get("PORT", 8000)
    ENVIRONMENT: str = os.environ.get("ENVIRONMENT", "local")
    API_V1_STR: str = "/v1"
    # SECRET_KEY: str = os.environ.get("SECRET_KEY")
    BACKEND_CORS_ORIGINS: List[str] = os.environ.get("BACKEND_CORS_ORIGINS", ["*"])
    SHOW_SWAGGER_DOC: bool = os.environ.get("SHOW_SWAGGER_DOC", True)
    # ACCESS_TOKEN_EXPIRE_MINUTES: int = os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
    # REFRESH_TOKEN_EXPIRE_MINUTES:int = os.environ.get("REFRESH_TOKEN_EXPIRE_MINUTES",900)

    # # opensearch resources
    OPENSEARCH_URL: str = os.environ["OPENSEARCH_URL"]
    OPENSEARCH_USERNAME: str = os.environ["OPENSEARCH_USERNAME"]
    OPENSEARCH_PASSWORD: str = os.environ["OPENSEARCH_PASSWORD"]
    OPENSEARCH_VECTOR_INDEX: str = os.environ["OPENSEARCH_VECTOR_INDEX"]

    # langchain resources
    LANGCHAIN_TRACING_V2: bool = os.environ.get("LANGCHAIN_TRACING_V2", True)
    LANGCHAIN_ENDPOINT: str = os.environ.get("LANGCHAIN_ENDPOINT")
    LANGCHAIN_API_KEY: str = os.environ.get("LANGCHAIN_API_KEY")
    LANGCHAIN_PROJECT: str = os.environ.get("LANGCHAIN_PROJECT")

    # openai resources
    # OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY")

    # # azure openai resources
    # AZURE_OPENAI_DEPLOYMENT_ENDPOINT: str = os.environ.get(
    #     "AZURE_OPENAI_DEPLOYMENT_ENDPOINT"
    # )
    # AZURE_OPENAI_DEPLOYMENT_VERSION: str = os.environ.get(
    #     "AZURE_OPENAI_DEPLOYMENT_VERSION"
    # )
    # AZURE_OPENAI_API_KEY: str = os.environ.get("AZURE_OPENAI_API_KEY")
    # AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME"
    # )
    # AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME"
    # )
    # AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"
    # )

    # AWS resources
    AWS_ACCESS_KEY_ID: str = os.environ.get("AWS_ACCESS_KEY_ID","")
    AWS_SECRET_ACCESS_KEY: str = os.environ.get("AWS_SECRET_ACCESS_KEY","")
    AWS_SESSION_TOKEN: str = os.environ.get("AWS_SESSION_TOKEN","")

    AWS_REGION: str = os.environ.get("AWS_REGION", "us-east-1")
    AWS_DEFAULT_REGION:str =os.environ.get("AWS_DEFAULT_REGION", "us-east-1")
    SQS_QUEUE_URL: str = os.environ.get("SQS_QUEUE_URL")
    S3_BUCKET_NAME: str = os.environ.get("S3_BUCKET_NAME")
    S3_FOLDER_PREFIX: str = os.environ.get("S3_FOLDER_PREFIX", "")
    # AWS_CLOUDWATCH_LOG_GROUP: str = os.environ.get(
    #     "AWS_CLOUDWATCH_LOG_GROUP", "conversational-ai-api"
    # )
    # AWS_CLOUDWATCH_LOG_STREAM: str = os.environ.get(
    #     "AWS_CLOUDWATCH_LOG_STREAM", "conversational-ai-api-ls"
    # )

    # # Redis (Optional)
    # REDIS_URL: str = os.environ.get("REDIS_URL")
    # REDIS_PORT: str = os.environ.get("REDIS_PORT")
    # REDIS_TTL: int = os.environ.get("REDIS_TTL", 60 * 60 * 24 * 7)

    class Config:
        case_sensitive = True


settings: Settings = Settings()
