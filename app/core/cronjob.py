from fastapi_utilities import repeat_at

from app.core.configuration import settings
from app.core.opensearch import os_client

# Define the indices you want to warm up
INDICES_TO_WARMUP = [settings.OPENSEARCH_VECTOR_INDEX]


@repeat_at(cron="*/30 * * * *")  # every 30 min minute
def index_warmup_cron():
    # Execute the warmup API operation
    os_client.transport.perform_request(
        "GET", "/_plugins/_knn/warmup/" + ",".join(INDICES_TO_WARMUP)
    )
