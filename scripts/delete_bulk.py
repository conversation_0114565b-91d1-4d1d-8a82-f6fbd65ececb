import logging
import os
from typing import Any, Dict, List

from publisher.run import list_s3_files

from app.core.configuration import settings
from scripts.create_index import _setup_opensearch_client

# Initialize the logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# AWS credentials and region configuration
s3_bucket_name = settings.S3_BUCKET_NAME
files_list = list_s3_files(bucket_name=s3_bucket_name, folder_prefix="sample_xml")

opensearch_client = _setup_opensearch_client(
    host=settings.OPENSEARCH_URL,
    username=settings.OPENSEARCH_USERNAME,
    password=settings.OPENSEARCH_PASSWORD,
)


def delete_file_documents_bulk(document_ids: List[str]) -> None:
    """
    The `delete_file_documents_bulk` function performs a bulk delete operation on a specified list of
    document IDs in an OpenSearch index.

    :param document_ids: A list of document IDs that need to be deleted
    :type document_ids: List[str]
    """
    bulk_delete_request = []
    for doc_id in document_ids:
        bulk_delete_request.extend(
            [
                {"delete": {"_index": settings.OPENSEARCH_VECTOR_INDEX, "_id": doc_id}},
            ]
        )

    # Perform the bulk delete operation
    opensearch_response = opensearch_client.bulk(
        body=bulk_delete_request, index=settings.OPENSEARCH_VECTOR_INDEX
    )

    # Check the response
    if opensearch_response["errors"]:
        for item in opensearch_response["items"]:
            if "delete" in item and item["delete"]["result"] != "deleted":
                logger.exception(f"Document {item['delete']['_id']} not deleted")
    else:
        logger.info(f"Bulk delete successful for {len(document_ids)} documents")


def get_file_documents(file: str) -> List[Dict[str, Any]]:
    """
    The function `get_file_documents` retrieves a list of documents from an OpenSearch index based on a
    file name.

    :param file: The `file` parameter is a string that represents the name of a file
    :type file: str
    :return: The function `get_file_documents` returns a list of dictionaries, where each dictionary
    represents a document related to the given file.
    """
    logger.info(f"file_name:{file}")

    file_filter_query = {
        "_source": ["metadata.source"],
        "size": 300,
        "query": {"term": {"metadata.source.keyword": os.path.basename(file)}},
    }

    count_query = {
        "query": {"term": {"metadata.source.keyword": os.path.basename(file)}}
    }

    count_response = opensearch_client.count(
        index=settings.OPENSEARCH_VECTOR_INDEX, body=count_query
    )
    total_count = count_response.get("count")

    opensearch_response = opensearch_client.search(
        index=settings.OPENSEARCH_VECTOR_INDEX, body=file_filter_query
    )
    file_query_count = opensearch_response["hits"]["total"]["value"]

    if file_query_count != total_count:
        logger.info("total count not matched for {file}")

    documents_list = opensearch_response["hits"]["hits"]

    return documents_list


for file in files_list:
    # get the metadata for that file
    documents_list = get_file_documents(file)

    document_ids = []
    for doc in documents_list:
        document_ids.append(doc["_id"])

    delete_file_documents_bulk(document_ids)
