from typing import Any, Dict

from opensearchpy import OpenSearch, exceptions

from app.core.configuration import settings
from loguru import logger

DIMENSIONS = 1536
M = 16
EF_CONSTRUCTION = 512
EF_SEARCH = 512
NO_OF_SHARDS = 5  # default opensearch number of shards
NO_OF_REPLICAS = 0
DISTANCE_METRICS = "cosinesimil"


VECTOR_INDEX_CONFIGURATION = {
    "mappings": {
        "properties": {
            "text": {
                "type": "text",
                "fields": {"keyword": {"type": "keyword", "ignore_above": 256}},
            },
            "vector_field": {
                "type": "knn_vector",
                "dimension": DIMENSIONS,
                "method": {
                    "engine": "nmslib",
                    "space_type": "cosinesimil",
                    "name": "hnsw",
                    "parameters": {"ef_construction": EF_CONSTRUCTION, "m": M},
                },
            },
            "metadata": {
                "properties": {
                    "source": {
                        "type": "text",
                        "fields": {"keyword": {"type": "keyword", "ignore_above": 256}},
                    }
                }
            },
        }
    },
    "settings": {
        "index": {
            "number_of_shards": f"{NO_OF_SHARDS}",
            "knn.algo_param": {"ef_search": f"{EF_SEARCH}"},
            "knn": "true",
            "number_of_replicas": f"{NO_OF_REPLICAS}",
        }
    },
}

USER_INDEX_MAPPING = {
    "settings": {"number_of_shards": 2, "number_of_replicas": 1},
    "mappings": {
        "properties": {
            "user_id": {"type": "keyword"},
            "role": {"type": "keyword"},
            "first_name": {"type": "text"},
            "last_name": {"type": "text"},
            "email": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "created_at": {"type": "date"},
            "updated_at": {"type": "date"},
            "updated_by": {"type": "keyword"},
            "last_login_at": {"type": "date"},
        }
    },
}

MESSAGE_INDEX_MAPPING = {
    "settings": {"number_of_shards": 2, "number_of_replicas": 1},
    "mappings": {
        "properties": {
            "is_successful": {"type": "boolean"},
            "answer": {"type": "text", "index": False},
            "answer_at": {"type": "date"},
            "feedback": {"type": "text", "index": False},
            "llm_model": {"type": "keyword"},
            "question": {"type": "text", "index": False},
            "question_at": {"type": "date"},
            "rating": {"type": "integer", "ignore_malformed": True},
            "response_time": {"type": "float"},
        }
    },
}


def _setup_opensearch_client(host: str, username: str, password: str, **kwargs):
    """
    The function `_setup_opensearch_client` sets up a connection to an OpenSearch instance using the
    provided host, username, and password.

    :param host: The `host` parameter is a string that represents the hostname or IP address of your
    OpenSearch instance. It specifies the location where the OpenSearch client should connect to
    :type host: str
    :param username: The `username` parameter is a string that represents the username used to
    authenticate with the OpenSearch instance
    :type username: str
    :param password: The `password` parameter is a string that represents the password used to
    authenticate the connection to the OpenSearch instance
    :type password: str
    :return: an instance of the OpenSearch client.
    """
    # Connect to your OpenSearch instance
    client = OpenSearch(hosts=host, http_auth=(username, password), **kwargs)
    return client


def create_index(
    _opensearch_client: OpenSearch, _index_name: str, _index_mapping: Dict[str, Any]
):
    # Create the index with the specified mapping
    res = _opensearch_client.indices.create(index=_index_name, body=_index_mapping)
    return res


if __name__ == "__main__":
    logger.info("Start: Creating Index with Mapping")
    try:
        opensearch_client = _setup_opensearch_client(
            host=settings.OS_URL,
            username=settings.OS_USERNAME,
            password=settings.OS_PASSWORD,
        )
        create_index(
            opensearch_client,
            settings.OPENSEARCH_VECTOR_INDEX,
            VECTOR_INDEX_CONFIGURATION,
        )

        logger.info("index created successfully")
    except exceptions.ConnectionError as e:
        logger.exception(f"Connection error: {e}")
    except exceptions.RequestError as e:
        logger.exception(f"Request error: {e}")
    except exceptions.TransportError as e:
        logger.exception(f"Transport error: {e}")
    except Exception as e:
        logger.exception(f"An error occurred: {e}")
